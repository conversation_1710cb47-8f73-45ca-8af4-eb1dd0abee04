# Video Recorder Error Test Interface

This test interface allows you to manually trigger and test the error handling mechanisms implemented for the video recorder extension's batch processing feature.

## 📁 Files

- **`error-test-overlay.html`** - Standalone HTML test interface
- **`inject-test-overlay.js`** - Console injection script
- **`ERROR_TEST_INTERFACE_README.md`** - This documentation

## 🚀 Usage Methods

### Method 1: Console Injection (Recommended)

1. **Load a course page** where the video recorder extension is active
2. **Open browser console** (F12 → Console tab)
3. **Copy and paste** the entire contents of `inject-test-overlay.js`
4. **Press Enter** to execute
5. **Test overlay appears** in the top-right corner

### Method 2: Direct HTML File

1. **Open** `error-test-overlay.html` in your browser
2. **Navigate to** the course page in another tab
3. **Use the interface** to test error handling

## 🧪 Test Interface Features

### 📊 Extension Status Section
- **Batch Processing Status**: Shows if batch recording is active
- **Video Element Detection**: Confirms video elements are found
- **Recorder State**: Displays current recording status
- **Refresh Button**: Updates all status information

### ⚠️ Error Simulation Section
- **Error Type Dropdown**: Select from 9 different error types:
  - `invalid_blob_url` - Simulates stale video URLs
  - `no_video_element` - No video found on page
  - `no_video_source` - Video has no source URL
  - `mediarecorder_error` - MediaRecorder API failure
  - `stream_capture_error` - Video stream capture failure
  - `video_playback_error` - Video play() method failure
  - `video_not_ready` - Video readiness state insufficient
  - `no_duration` - Video has no valid duration
  - `video_error` - Video element network/decode errors

- **Trigger Error Button**: Simulates the selected error type
- **Test Recovery Button**: Creates fake recovery state and refreshes page

### 💾 Recovery State Section
- **Recovery Status Display**: Shows current recovery state from Chrome storage
- **Clear Recovery Button**: Removes all recovery state data
- **Force Refresh Button**: Manually refreshes the page

### 📝 Activity Log Section
- **Real-time Logging**: Shows all test activities with timestamps
- **Color-coded Messages**: Error (red), Success (green), Info (blue), Warning (orange)
- **Clear Log Button**: Resets the activity log

## ⌨️ Keyboard Shortcuts

- **Ctrl+Shift+T** - Toggle overlay visibility
- **Ctrl+Shift+E** - Trigger error simulation
- **Ctrl+Shift+R** - Test recovery mechanism

## 🎯 Testing Workflow

### Basic Error Testing
1. **Start batch recording** using the extension popup
2. **Open test interface** using console injection
3. **Verify extension status** shows "Batch Processing: Active"
4. **Select error type** from dropdown
5. **Click "Trigger Error"** button
6. **Observe page refresh** and recovery process
7. **Verify batch continues** from correct position

### Recovery State Testing
1. **Click "Test Recovery"** button
2. **Page refreshes** with fake recovery state
3. **Verify extension detects** recovery state
4. **Check popup UI** shows resumed batch processing
5. **Confirm recovery state** is cleared after successful resumption

### Advanced Testing
1. **Use console access**: `window.errorTestInterface.simulateError()`
2. **Test specific scenarios**: Different error types during different batch states
3. **Validate state persistence**: Check Chrome storage before/after refresh
4. **Monitor console logs**: Extension debug messages during recovery

## 🔧 Console API

Access advanced functions via `window.errorTestInterface`:

```javascript
// Simulate specific error
window.errorTestInterface.simulateError()

// Test recovery mechanism
window.errorTestInterface.simulateRecovery()

// Clear recovery state
window.errorTestInterface.clearRecoveryState()

// Force page refresh
window.errorTestInterface.forcePageRefresh()

// Refresh status display
window.errorTestInterface.refreshStatus()

// Log custom message
window.errorTestInterface.logActivity('Custom message', 'info')

// Clear activity log
window.errorTestInterface.clearLog()
```

## 🎨 Interface Controls

### Draggable Overlay
- **Click and drag** the header to move the overlay
- **Stays within** viewport boundaries
- **Remembers position** during session

### Collapsible Design
- **Click "−" button** to collapse to header only
- **Click "+" button** to expand full interface
- **Saves space** while keeping access available

### Auto-refresh Status
- **Updates every 5 seconds** when expanded
- **Shows real-time** extension state changes
- **Monitors recovery state** automatically

## ⚠️ Prerequisites

1. **Video Recorder Extension** must be loaded and active
2. **Course page** with video content must be open
3. **Chrome storage** must be accessible
4. **Content script** must be injected by extension

## 🐛 Troubleshooting

### "Extension not detected"
- Ensure video recorder extension is installed and enabled
- Refresh the page and try again
- Check if content script is loaded

### "Chrome storage not available"
- Extension may not have proper permissions
- Try reloading the extension
- Check browser console for errors

### Error simulation not working
- Verify batch processing is active
- Check if `handleRecordingFailure` function exists
- Ensure proper extension state

### Recovery test fails
- Clear existing recovery state first
- Ensure Chrome storage permissions
- Check console for error messages

## 📋 Test Checklist

- [ ] Extension status shows all green indicators
- [ ] Error simulation triggers page refresh
- [ ] Recovery state is properly stored/cleared
- [ ] Batch processing resumes from correct position
- [ ] UI updates reflect recovery progress
- [ ] Console logs show proper error handling
- [ ] All error types can be simulated
- [ ] Keyboard shortcuts work correctly
- [ ] Interface is draggable and collapsible
- [ ] Activity log captures all events

This test interface provides comprehensive testing capabilities for the video recorder extension's error handling and recovery mechanisms, ensuring robust batch processing functionality even when video URLs become invalid.
