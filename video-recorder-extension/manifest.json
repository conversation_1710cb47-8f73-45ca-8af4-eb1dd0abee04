{"manifest_version": 3, "name": "Video Recorder Extension", "version": "1.0", "description": "Record videos from web pages with advanced codec and quality options", "permissions": ["activeTab", "storage", "scripting", "downloads"], "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_idle"}]}