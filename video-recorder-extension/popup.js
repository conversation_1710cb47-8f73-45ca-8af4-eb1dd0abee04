// Detect operating system
function detectOS() {
  const userAgent = navigator.userAgent.toLowerCase();
  if (userAgent.indexOf('win') !== -1) return 'windows';
  if (userAgent.indexOf('mac') !== -1) return 'macos';
  if (userAgent.indexOf('linux') !== -1) return 'linux';
  return 'unknown';
}

// Get the current operating system
const currentOS = detectOS();
console.log(`🖥️ Detected operating system in popup: ${currentOS}`);

document.addEventListener('DOMContentLoaded', function() {
  // Get UI elements
  const startButton = document.getElementById('start-recording');
  const pauseResumeButton = document.getElementById('pause-resume');
  const stopButton = document.getElementById('stop-recording');
  const setupForm = document.getElementById('setup-form');
  const recordingControls = document.getElementById('recording-controls');
  const recordingStatus = document.getElementById('recording-status');
  const statusElement = document.getElementById('status');

  // Batch recording status elements
  const batchRecordingStatusCard = document.getElementById('batch-recording-status-card');
  const batchCurrentVideoTitle = document.getElementById('batch-current-video-title');
  const batchRecordingStatus = document.getElementById('batch-recording-status');
  const batchPauseResumeButton = document.getElementById('batch-pause-resume');
  const batchStopButton = document.getElementById('batch-stop-recording');

  // Tab navigation elements
  const tabButtons = document.querySelectorAll('.tab-button');
  const tabContents = document.querySelectorAll('.tab-content');

  // Course structure elements
  const parseCourseButton = document.getElementById('parse-course-structure');
  const courseTreeContainer = document.getElementById('course-tree-container');
  const courseTree = document.getElementById('course-tree');
  const recordSelectedButton = document.getElementById('record-selected');
  const selectAllVideosButton = document.getElementById('select-all-videos');
  const deselectAllButton = document.getElementById('deselect-all');
  const batchProgress = document.getElementById('batch-progress');
  const batchProgressBar = document.getElementById('batch-progress-bar');
  const batchProgressText = document.getElementById('batch-progress-text');
  const batchStatus = document.getElementById('batch-status');

  // Course structure data
  let courseStructure = null;
  let currentBatchItems = [];
  let currentBatchIndex = 0;
  let isProcessingBatch = false;

  // Load saved settings
  chrome.storage.sync.get({
    codec: 'vp9',
    quality: 'high',
    autoStop: true,
    autoDownload: true
  }, function(items) {
    document.getElementById('codec-select').value = items.codec;
    document.getElementById('quality-select').value = items.quality;
    document.getElementById('auto-stop-checkbox').checked = items.autoStop;
    document.getElementById('auto-download-checkbox').checked = items.autoDownload;

    // Also set batch recording options
    document.getElementById('batch-auto-stop-checkbox').checked = items.autoStop;
    document.getElementById('batch-auto-download-checkbox').checked = items.autoDownload;
  });

  // Tab navigation
  tabButtons.forEach(button => {
    button.addEventListener('click', function() {
      // Remove active class from all buttons and contents
      tabButtons.forEach(btn => btn.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));

      // Add active class to clicked button and corresponding content
      this.classList.add('active');
      const tabId = this.getAttribute('data-tab') + '-tab';
      document.getElementById(tabId).classList.add('active');
    });
  });

  // Check if recording is already in progress when popup opens
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    chrome.tabs.sendMessage(tabs[0].id, {
      action: 'getRecordingState'
    }, function(response) {
      if (response?.isRecording) {
        // Show recording controls
        setupForm.style.display = 'none';
        recordingControls.style.display = 'block';
        // Show and set current video title
        const currentVideoTitleDiv = document.getElementById('current-video-title');
        if (currentVideoTitleDiv) {
          chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {action: 'getVideoTitle'}, function(res) {
              let title = res && res.title ? res.title : tabs[0].title;
              currentVideoTitleDiv.textContent = 'Recording: ' + title;
              currentVideoTitleDiv.style.display = 'block';
            });
          });
        }

        // Update pause/resume button state
        if (response.isPaused) {
          pauseResumeButton.textContent = 'Resume';
          pauseResumeButton.className = '';
          recordingStatus.textContent = 'Paused';
        } else {
          pauseResumeButton.textContent = 'Pause';
          pauseResumeButton.className = 'pause-button';
          recordingStatus.textContent = response.status || 'Recording...';
        }
      }
    });
  });

  // Start recording button
  startButton.addEventListener('click', function() {
    // Get selected options
    const codec = document.getElementById('codec-select').value;
    const quality = document.getElementById('quality-select').value;
    const autoStop = document.getElementById('auto-stop-checkbox').checked;
    const autoDownload = document.getElementById('auto-download-checkbox').checked;

    // Save settings
    chrome.storage.sync.set({
      codec: codec,
      quality: quality,
      autoStop: autoStop,
      autoDownload: autoDownload
    });

    // Send message to content script to start recording
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'startRecording',
        options: {
          codec: codec,
          quality: quality,
          autoStop: autoStop,
          autoDownload: autoDownload
        },
        os: currentOS  // Pass OS information to content script
      }, function(response) {
        if (response?.success) {
          // Show recording controls
          setupForm.style.display = 'none';
          recordingControls.style.display = 'block';
          // Show and set current video title
          const currentVideoTitleDiv = document.getElementById('current-video-title');
          if (currentVideoTitleDiv) {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
              chrome.tabs.sendMessage(tabs[0].id, {action: 'getVideoTitle'}, function(res) {
                let title = res && res.title ? res.title : tabs[0].title;
                currentVideoTitleDiv.textContent = 'Recording: ' + title;
                currentVideoTitleDiv.style.display = 'block';
              });
            });
          }
          recordingStatus.textContent = 'Recording...';

          // Show success message
          showStatus('Recording started successfully!', 'success');
        } else {
          // Show error message
          showStatus('Failed to start recording: ' + (response?.error || 'No response from page'), 'error');
        }
      });
    });
  });

  // Pause/Resume button
  pauseResumeButton.addEventListener('click', function() {
    const isPaused = pauseResumeButton.textContent === 'Resume';

    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: isPaused ? 'resumeRecording' : 'pauseRecording'
      }, function(response) {
        if (response?.success) {
          pauseResumeButton.textContent = isPaused ? 'Pause' : 'Resume';
          pauseResumeButton.className = isPaused ? 'pause-button' : '';
          recordingStatus.textContent = isPaused ? 'Recording...' : 'Paused';
        } else {
          showStatus('Failed to ' + (isPaused ? 'resume' : 'pause') + ' recording', 'error');
        }
      });
    });
  });

  // Stop recording button
  stopButton.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'stopRecording'
      }, function(response) {
        if (response?.success) {
          // Reset UI
          setupForm.style.display = 'block';
          recordingControls.style.display = 'none';
          pauseResumeButton.textContent = 'Pause';
          pauseResumeButton.className = 'pause-button';
          // Hide current video title
          const currentVideoTitleDiv = document.getElementById('current-video-title');
          if (currentVideoTitleDiv) {
            currentVideoTitleDiv.style.display = 'none';
          }

          // Show success message
          showStatus('Recording stopped. ' + (response.downloadStarted ? 'Download started automatically.' : 'Check the page for download link.'), 'success');

          // If we're processing a batch, move to the next item
          if (isProcessingBatch) {
            processBatchNextItem();
          }
        } else {
          showStatus('Failed to stop recording', 'error');

          // If we're processing a batch, handle the error and try to continue
          if (isProcessingBatch) {
            batchStatus.textContent = 'Error with current video, moving to next...';
            setTimeout(processBatchNextItem, 3000);
          }
        }
      });
    });
  });

  // Parse course structure button
  parseCourseButton.addEventListener('click', function() {
    courseTree.innerHTML = '<div class="loading-message">Loading course structure...</div>';
    courseTreeContainer.style.display = 'block';

    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'parseCourseStructure'
      }, function(response) {
        if (response?.success) {
          courseStructure = response.courseStructure;
          renderCourseTree(courseStructure);
          // Cache the structure
          chrome.storage.local.set({courseStructure: courseStructure});
          showStatus('Course structure loaded successfully!', 'success');
        } else {
          courseTree.innerHTML = '<div class="error-message">Failed to parse course structure. Make sure you are on a course page.</div>';
          showStatus('Failed to parse course structure: ' + (response?.error || 'No response from page'), 'error');
        }
      });
    });
  });

  // Refresh course structure button
  const refreshCourseButton = document.getElementById('refresh-course-structure');
  refreshCourseButton.addEventListener('click', function() {
    courseTree.innerHTML = '<div class="loading-message">Refreshing course structure...</div>';
    courseTreeContainer.style.display = 'block';
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'parseCourseStructure'
      }, function(response) {
        if (response?.success) {
          courseStructure = response.courseStructure;
          renderCourseTree(courseStructure);
          chrome.storage.local.set({courseStructure: courseStructure});
          showStatus('Course structure refreshed!', 'success');
        } else {
          courseTree.innerHTML = '<div class="error-message">Failed to refresh course structure. Make sure you are on a course page.</div>';
          showStatus('Failed to refresh course structure: ' + (response?.error || 'No response from page'), 'error');
        }
      });
    });
  });

  // Function to check for recovery state and resume batch processing
  function checkForRecoveryState() {
    chrome.storage.local.get(['refreshPending', 'batchItems', 'batchState', 'recordingOptions'], function(result) {
      if (result.refreshPending && result.batchItems && result.batchItems.length > 0) {
        console.log("🔄 Recovery state detected in popup, batch processing was in progress");

        // Restore batch processing state
        currentBatchItems = result.batchItems;
        currentBatchIndex = result.batchState?.currentBatchIndex || 0;
        isProcessingBatch = true;

        // Show batch progress UI
        batchProgress.style.display = 'block';
        batchProgressBar.style.width = `${(currentBatchIndex / currentBatchItems.length) * 100}%`;
        batchProgressText.textContent = `${currentBatchIndex}/${currentBatchItems.length} videos processed`;
        batchStatus.textContent = 'Resuming batch processing after page refresh...';

        console.log(`📊 Restored batch state: index=${currentBatchIndex}, total=${currentBatchItems.length}`);
        showStatus('Batch processing resumed after page refresh', 'info');

        // Note: The actual resumption will be handled by content script
        // We just need to restore the UI state here
      }
    });
  }

  // On popup load, try to load cached course structure and check for recovery
  chrome.storage.local.get('courseStructure', function(result) {
    if (result && result.courseStructure && result.courseStructure.sections && result.courseStructure.sections.length > 0) {
      courseStructure = result.courseStructure;
      renderCourseTree(courseStructure);
      courseTreeContainer.style.display = 'block';
      showStatus('Course structure loaded from cache', 'success');
    }

    // Check for recovery state after loading course structure
    checkForRecoveryState();
  });

  // Record selected videos button
  recordSelectedButton.addEventListener('click', function() {
    if (isProcessingBatch) {
      showStatus('Already processing videos. Please wait.', 'error');
      return;
    }

    // Get selected items
    const selectedItems = getSelectedItems();

    if (selectedItems.length === 0) {
      showStatus('No videos selected. Please select at least one video.', 'error');
      return;
    }

    // Get batch recording options
    const batchOptions = {
      codec: document.getElementById('codec-select').value,
      quality: document.getElementById('quality-select').value,
      autoStop: document.getElementById('batch-auto-stop-checkbox').checked,
      autoDownload: document.getElementById('batch-auto-download-checkbox').checked
    };

    // Start batch processing
    startBatchProcessing(selectedItems, batchOptions);
  });

  // Select all videos button
  selectAllVideosButton.addEventListener('click', function() {
    const videoCheckboxes = document.querySelectorAll('.item-checkbox[data-type="video"]');
    videoCheckboxes.forEach(checkbox => {
      checkbox.checked = true;
    });

    // Also update section checkboxes based on their items
    updateSectionCheckboxes();
  });

  // Deselect all button
  deselectAllButton.addEventListener('click', function() {
    const allCheckboxes = document.querySelectorAll('.item-checkbox, .section-checkbox');
    allCheckboxes.forEach(checkbox => {
      checkbox.checked = false;
    });
  });

  // Listen for messages from content script
  chrome.runtime.onMessage.addListener(function(request, _sender, sendResponse) {
    console.log("📥 Popup received message:", request.action, request);

    if (request.action === 'updateStatus') {
      recordingStatus.textContent = request.status;
      // Also update batch recording status if it's visible
      if (batchRecordingStatusCard.style.display !== 'none') {
        batchRecordingStatus.textContent = request.status;
      }
    } else if (request.action === 'recordingStopped') {
      console.log("🛑 Recording stopped message received in popup",
                 "isBatch:", request.isBatch,
                 "isProcessingBatch:", isProcessingBatch,
                 "forceNextVideo:", request.forceNextVideo,
                 "autoStopTriggered:", request.autoStopTriggered);

      // Reset UI
      setupForm.style.display = 'block';
      recordingControls.style.display = 'none';
      pauseResumeButton.textContent = 'Pause';
      pauseResumeButton.className = 'pause-button';

      // Hide the batch recording status card if it's visible
      if (batchRecordingStatusCard.style.display !== 'none') {
        batchRecordingStatusCard.style.display = 'none';
      }

      // Show message
      showStatus('Recording stopped. ' + (request.downloadStarted ? 'Download started automatically.' : 'Check the page for download link.'), 'success');

      // If we're processing a batch, move to the next item
      if (isProcessingBatch || request.isBatch || request.forceNextVideo) {
        console.log("🔄 Processing next batch item",
                   "isProcessingBatch:", isProcessingBatch,
                   "isBatch:", request.isBatch,
                   "forceNextVideo:", request.forceNextVideo);
        processBatchNextItem();
      }
    } else if (request.action === 'courseStructureUpdated') {
      // Update the course structure if it's been updated from the content script
      if (courseStructure) {
        courseStructure = request.courseStructure;
        renderCourseTree(courseStructure);
      }
    } else if (request.action === 'batchRecoveryStarted') {
      // Handle batch recovery started notification from content script
      console.log("🔄 Batch recovery started notification received");

      if (request.currentItem) {
        batchStatus.textContent = `Resuming: ${request.currentItem.itemTitle}`;

        // Show the batch recording status card
        batchRecordingStatusCard.style.display = 'block';
        batchCurrentVideoTitle.textContent = `Recording: ${request.currentItem.itemTitle}`;
        batchRecordingStatus.textContent = 'Resuming after refresh...';
      }
    } else if (request.action === 'batchRecoveryCompleted') {
      // Handle batch recovery completion notification from content script
      console.log("✅ Batch recovery completed notification received");

      // Clear recovery state since content script successfully resumed
      chrome.storage.local.remove(['refreshPending', 'recoveryBatchIndex', 'recoveryRetryCount', 'recoveryTimestamp'], function() {
        console.log("🧹 Cleared recovery state after successful resumption");
      });

      batchStatus.textContent = 'Recording resumed successfully';
    }

    console.log("📤 Sending response for message:", request.action);
    sendResponse({received: true});
  });

  // Helper function to show status messages
  function showStatus(message, type) {
    statusElement.textContent = message;
    statusElement.className = type;
    statusElement.style.display = 'block';

    setTimeout(function() {
      statusElement.style.display = 'none';
    }, 5000);
  }

  // Helper function to render the course tree
  function renderCourseTree(courseData) {
    if (!courseData || !courseData.sections || courseData.sections.length === 0) {
      courseTree.innerHTML = '<div class="error-message">No course sections found.</div>';
      return;
    }

    // Create course title
    const courseTitle = document.createElement('div');
    courseTitle.className = 'course-title';
    courseTitle.textContent = courseData.title || 'Course Structure';

    // Clear and add course title
    courseTree.innerHTML = '';
    courseTree.appendChild(courseTitle);

    // Create sections
    courseData.sections.forEach((section, sectionIndex) => {
      const sectionElement = document.createElement('div');
      sectionElement.className = 'tree-item';
      sectionElement.dataset.sectionIndex = sectionIndex;

      const sectionHeader = document.createElement('div');
      sectionHeader.className = 'section';

      const sectionToggle = document.createElement('span');
      sectionToggle.className = 'section-toggle';
      sectionToggle.textContent = '▶';
      sectionToggle.addEventListener('click', function(e) {
        e.stopPropagation();
        const sectionItems = sectionElement.querySelector('.section-items');
        if (sectionItems.classList.contains('expanded')) {
          sectionItems.classList.remove('expanded');
          sectionToggle.textContent = '▶';
        } else {
          sectionItems.classList.add('expanded');
          sectionToggle.textContent = '▼';
        }
      });

      const sectionCheckbox = document.createElement('input');
      sectionCheckbox.type = 'checkbox';
      sectionCheckbox.className = 'section-checkbox';
      sectionCheckbox.dataset.sectionIndex = sectionIndex;
      sectionCheckbox.addEventListener('change', function() {
        // When section checkbox changes, update all items in this section
        const items = sectionElement.querySelectorAll('.item-checkbox');
        items.forEach(item => {
          item.checked = this.checked;
        });
      });

      const sectionTitle = document.createElement('span');
      sectionTitle.className = 'section-title';
      sectionTitle.textContent = section.title;
      sectionTitle.addEventListener('click', function() {
        // Toggle section expansion when clicking on title
        sectionToggle.click();
      });

      sectionHeader.appendChild(sectionToggle);
      sectionHeader.appendChild(sectionCheckbox);
      sectionHeader.appendChild(sectionTitle);

      const sectionItems = document.createElement('div');
      sectionItems.className = 'section-items';

      // Create items
      if (section.items && section.items.length > 0) {
        // Count video items
        const videoCount = section.items.filter(item => item.type === 'video').length;
        const pdfCount = section.items.filter(item => item.type === 'pdf').length;
        const otherCount = section.items.length - videoCount - pdfCount;

        // Add item counts to section title if there are items
        if (videoCount > 0 || pdfCount > 0) {
          const countSpan = document.createElement('span');
          countSpan.style.fontSize = '12px';
          countSpan.style.color = '#666';
          countSpan.style.marginLeft = '8px';
          countSpan.textContent = `(${videoCount} videos${pdfCount > 0 ? `, ${pdfCount} PDFs` : ''}${otherCount > 0 ? `, ${otherCount} other` : ''})`;
          sectionTitle.appendChild(countSpan);
        }

        section.items.forEach((item, itemIndex) => {
          const itemElement = document.createElement('div');
          itemElement.className = 'item';

          const itemCheckbox = document.createElement('input');
          itemCheckbox.type = 'checkbox';
          itemCheckbox.className = 'item-checkbox';
          itemCheckbox.dataset.sectionIndex = sectionIndex;
          itemCheckbox.dataset.itemIndex = itemIndex;
          itemCheckbox.dataset.type = item.type;
          itemCheckbox.addEventListener('change', function() {
            // When an item checkbox changes, update the section checkbox
            updateSectionCheckbox(sectionIndex);
          });

          const itemTitle = document.createElement('span');
          itemTitle.className = 'item-title';
          itemTitle.textContent = item.title;

          const itemType = document.createElement('span');
          itemType.className = `item-type ${item.type}`;
          itemType.textContent = item.type.toUpperCase();

          itemElement.appendChild(itemCheckbox);
          itemElement.appendChild(itemTitle);
          itemElement.appendChild(itemType);

          sectionItems.appendChild(itemElement);
        });

        // All sections collapsed by default (removed auto-expand of first section)
      } else {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-message';
        emptyMessage.textContent = 'No items in this section';
        sectionItems.appendChild(emptyMessage);
      }

      sectionElement.appendChild(sectionHeader);
      sectionElement.appendChild(sectionItems);

      courseTree.appendChild(sectionElement);
    });

    // Show a summary of the course structure
    const totalVideos = courseData.sections.reduce((count, section) => {
      return count + section.items.filter(item => item.type === 'video').length;
    }, 0);

    const totalPDFs = courseData.sections.reduce((count, section) => {
      return count + section.items.filter(item => item.type === 'pdf').length;
    }, 0);

    const summaryDiv = document.createElement('div');
    summaryDiv.style.marginTop = '10px';
    summaryDiv.style.fontSize = '12px';
    summaryDiv.style.color = '#666';
    summaryDiv.style.textAlign = 'center';
    summaryDiv.innerHTML = `<strong>Course Summary:</strong> ${courseData.sections.length} sections, ${totalVideos} videos, ${totalPDFs} PDFs`;

    courseTree.appendChild(summaryDiv);
  }

  // Helper function to update a section checkbox based on its items
  function updateSectionCheckbox(sectionIndex) {
    const sectionCheckbox = document.querySelector(`.section-checkbox[data-section-index="${sectionIndex}"]`);
    const itemCheckboxes = document.querySelectorAll(`.item-checkbox[data-section-index="${sectionIndex}"]`);

    if (sectionCheckbox && itemCheckboxes.length > 0) {
      const allChecked = Array.from(itemCheckboxes).every(cb => cb.checked);
      const someChecked = Array.from(itemCheckboxes).some(cb => cb.checked);

      sectionCheckbox.checked = allChecked;
      sectionCheckbox.indeterminate = someChecked && !allChecked;
    }
  }

  // Helper function to update all section checkboxes
  function updateSectionCheckboxes() {
    const sectionIndices = new Set();
    document.querySelectorAll('.section-checkbox').forEach(cb => {
      sectionIndices.add(cb.dataset.sectionIndex);
    });

    sectionIndices.forEach(index => {
      updateSectionCheckbox(index);
    });
  }

  // Helper function to get selected items
  function getSelectedItems() {
    const selectedItems = [];

    document.querySelectorAll('.item-checkbox:checked').forEach(checkbox => {
      const sectionIndex = parseInt(checkbox.dataset.sectionIndex);
      const itemIndex = parseInt(checkbox.dataset.itemIndex);

      if (courseStructure &&
          courseStructure.sections &&
          courseStructure.sections[sectionIndex] &&
          courseStructure.sections[sectionIndex].items &&
          courseStructure.sections[sectionIndex].items[itemIndex]) {

        const section = courseStructure.sections[sectionIndex];
        const item = section.items[itemIndex];

        selectedItems.push({
          sectionIndex,
          itemIndex,
          sectionTitle: section.title,
          itemTitle: item.title,
          itemId: item.id,
          itemType: item.type
        });
      }
    });

    return selectedItems;
  }

  // Start batch processing
  function startBatchProcessing(items, options) {
    if (items.length === 0) return;

    // Filter to only include video items
    const videoItems = items.filter(item => item.itemType === 'video');

    if (videoItems.length === 0) {
      showStatus('No video items selected. Please select at least one video.', 'error');
      return;
    }

    // Check if there's an existing recovery state that should be cleared
    chrome.storage.local.get(['refreshPending'], function(result) {
      if (result.refreshPending) {
        console.log("🧹 Clearing existing recovery state before starting new batch");
        chrome.storage.local.remove(['refreshPending', 'recoveryBatchIndex', 'recoveryRetryCount', 'recoveryTimestamp']);
      }
    });

    // Log selected items for debugging
    console.log(`📋 Selected video items (${videoItems.length} total):`,
               videoItems.map((item, idx) => `${idx}: ${item.itemTitle} (Section: ${item.sectionTitle})`));

    // Print detailed information about each selected item
    console.log("📋 DETAILED SELECTED ITEMS:");
    videoItems.forEach((item, index) => {
      console.log(`Item ${index}: ${item.itemTitle}`);
      console.log(`  Section: ${item.sectionTitle}`);
      console.log(`  SectionIndex: ${item.sectionIndex}`);
      console.log(`  ItemIndex: ${item.itemIndex}`);
      console.log(`  ItemType: ${item.itemType}`);
    });

    // Set up batch processing
    currentBatchItems = videoItems;
    currentBatchIndex = 0;
    isProcessingBatch = true;

    // Store batch items and options in local storage for background script
    chrome.storage.local.set({
      batchItems: videoItems,
      recordingOptions: options,
      batchState: {
        currentBatchIndex: 0,
        totalItems: videoItems.length
      },
      currentOS: currentOS  // Store OS information for background script
    }, function() {
      // Print what was stored in storage
      console.log("STORED IN CHROME STORAGE:");
      console.log("  batchItems:", JSON.stringify(videoItems, null, 2));
      console.log("  batchState:", JSON.stringify({
        currentBatchIndex: 0,
        totalItems: videoItems.length
      }, null, 2));

      // Print each item in the batch items array
      console.log("ALL SELECTED ITEMS:");
      videoItems.forEach((item, index) => {
        console.log(`Item ${index}: ${item.itemTitle} (Section: ${item.sectionTitle}, SectionIndex: ${item.sectionIndex}, ItemIndex: ${item.itemIndex})`);
      });
    });

    // Show batch progress UI
    batchProgress.style.display = 'block';
    batchProgressBar.style.width = '0%';
    batchProgressText.textContent = `0/${videoItems.length} videos processed`;
    batchStatus.textContent = 'Starting batch processing...';

    // Start processing the first item
    processBatchItem(currentBatchIndex, options);
  }

  // Process a batch item
  function processBatchItem(index, options) {
    console.log("POPUP: processBatchItem called with index:", index);
    console.log("POPUP: currentBatchItems:", JSON.stringify(currentBatchItems, null, 2));

    if (index >= currentBatchItems.length) {
      // Batch processing complete
      console.log("POPUP: Batch processing complete, index >= currentBatchItems.length");
      finishBatchProcessing();
      return;
    }

    const item = currentBatchItems[index];
    console.log("POPUP: Processing item:", JSON.stringify(item, null, 2));
    batchStatus.textContent = `Processing: ${item.itemTitle}`;

    // Update progress
    const progress = (index / currentBatchItems.length) * 100;
    batchProgressBar.style.width = `${progress}%`;
    batchProgressText.textContent = `${index}/${currentBatchItems.length} videos processed`;

    // Show the batch recording status card at the top
    batchRecordingStatusCard.style.display = 'block';
    batchCurrentVideoTitle.textContent = `Recording: ${item.itemTitle}`;
    batchRecordingStatus.textContent = 'Recording...';

    // Set up batch pause/resume and stop buttons
    batchPauseResumeButton.textContent = 'Pause';
    batchPauseResumeButton.className = 'pause-button';

    // Add event listeners for batch recording controls if not already added
    if (!batchPauseResumeButton.hasEventListener) {
      batchPauseResumeButton.hasEventListener = true;
      batchPauseResumeButton.addEventListener('click', function() {
        const isPaused = batchPauseResumeButton.textContent === 'Resume';

        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
          chrome.tabs.sendMessage(tabs[0].id, {
            action: isPaused ? 'resumeRecording' : 'pauseRecording'
          }, function(response) {
            if (response?.success) {
              batchPauseResumeButton.textContent = isPaused ? 'Pause' : 'Resume';
              batchPauseResumeButton.className = isPaused ? 'pause-button' : '';
              batchRecordingStatus.textContent = isPaused ? 'Recording...' : 'Paused';
            } else {
              showStatus('Failed to ' + (isPaused ? 'resume' : 'pause') + ' recording', 'error');
            }
          });
        });
      });
    }

    if (!batchStopButton.hasEventListener) {
      batchStopButton.hasEventListener = true;
      batchStopButton.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
          chrome.tabs.sendMessage(tabs[0].id, {
            action: 'stopRecording'
          }, function(response) {
            if (response?.success) {
              // Hide the batch recording status card
              batchRecordingStatusCard.style.display = 'none';

              // Show success message
              showStatus('Recording stopped. ' + (response.downloadStarted ? 'Download started automatically.' : 'Check the page for download link.'), 'success');

              // If we're processing a batch, move to the next item
              if (isProcessingBatch) {
                processBatchNextItem();
              }
            } else {
              showStatus('Failed to stop recording', 'error');

              // If we're processing a batch, handle the error and try to continue
              if (isProcessingBatch) {
                batchStatus.textContent = 'Error with current video, moving to next...';
                setTimeout(processBatchNextItem, 3000);
              }
            }
          });
        });
      });
    }

    // Send message to content script to navigate to and record this item
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'processVideoItem',
        item: item,
        options: options,
        isBatch: true,  // Flag to indicate this is part of batch processing
        os: currentOS   // Pass OS information to content script
      }, function(response) {
        if (response?.success) {
          // Item is being processed, the content script will handle the recording
          // and will notify us when it's done via the recordingStopped message
          batchStatus.textContent = `Recording ${item.itemTitle}...`;
        } else {
          // Error processing item, move to next
          batchStatus.textContent = `Error processing ${item.itemTitle}: ${response?.error || 'Unknown error'}`;
          // Hide the batch recording status card
          batchRecordingStatusCard.style.display = 'none';
          setTimeout(function() {
            processBatchNextItem();
          }, 3000);
        }
      });
    });
  }

  // Process the next batch item with countdown
  function processBatchNextItem() {
    console.log("POPUP: processBatchNextItem called");
    console.log("POPUP: currentBatchIndex:", currentBatchIndex);
    console.log("POPUP: totalItems:", currentBatchItems.length);
    console.log("POPUP: currentBatchItems:", JSON.stringify(currentBatchItems, null, 2));

    // Update progress for the completed item
    const progress = (currentBatchIndex / currentBatchItems.length) * 100;
    batchProgressBar.style.width = `${progress}%`;
    batchProgressText.textContent = `${currentBatchIndex}/${currentBatchItems.length} videos processed`;

    // Check if we've completed all items
    if (currentBatchIndex >= currentBatchItems.length) {
      console.log("✅ Batch processing complete - all items processed");
      // Batch processing complete
      finishBatchProcessing();
      return;
    }

    console.log("⏱️ Starting countdown for next video");
    // Start countdown before moving to next video
    let countdown = 5;
    batchStatus.textContent = `Moving to next video in ${countdown}...`;

    const countdownInterval = setInterval(() => {
      countdown--;
      console.log(`⏱️ Popup countdown: ${countdown} seconds remaining`);

      if (countdown <= 0) {
        console.log("✅ Popup countdown complete, proceeding to next video");
        clearInterval(countdownInterval);

        // Increment the index after countdown completes
        currentBatchIndex++;
        console.log(`📊 Incremented batch index to ${currentBatchIndex}/${currentBatchItems.length}`);

        // Update batch state in storage for background script
        chrome.storage.local.set({
          batchState: {
            currentBatchIndex: currentBatchIndex,
            totalItems: currentBatchItems.length
          }
        });

        // Get the options again in case they were changed
        const options = {
          codec: document.getElementById('codec-select').value,
          quality: document.getElementById('quality-select').value,
          autoStop: document.getElementById('batch-auto-stop-checkbox').checked,
          autoDownload: document.getElementById('batch-auto-download-checkbox').checked
        };
        console.log("⚙️ Using options for next video:", options);

        // Process the next item
        processBatchItem(currentBatchIndex, options);
      } else {
        batchStatus.textContent = `Moving to next video in ${countdown}...`;
      }
    }, 1000);
  }

  // Finish batch processing
  function finishBatchProcessing() {
    isProcessingBatch = false;
    batchStatus.textContent = 'Batch processing complete!';
    batchProgressBar.style.width = '100%';
    batchProgressText.textContent = `${currentBatchItems.length}/${currentBatchItems.length} videos processed`;

    // Hide the batch recording status card
    batchRecordingStatusCard.style.display = 'none';

    // Clear any remaining recovery state
    chrome.storage.local.remove(['refreshPending', 'recoveryBatchIndex', 'recoveryRetryCount', 'recoveryTimestamp'], function() {
      console.log("🧹 Cleared recovery state after batch completion");
    });

    showStatus('Batch processing complete!', 'success');

    // Hide batch progress after a delay
    setTimeout(function() {
      batchProgress.style.display = 'none';
    }, 5000);
  }
});
