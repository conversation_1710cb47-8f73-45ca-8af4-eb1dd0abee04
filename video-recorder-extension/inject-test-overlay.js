/**
 * Injection Script for Video Recorder Error Test Interface
 * 
 * This script can be run in the browser console to inject the test overlay
 * into any webpage where the video recorder extension is running.
 * 
 * Usage:
 * 1. Open browser console (F12)
 * 2. Copy and paste this entire script
 * 3. Press Enter to execute
 * 4. The test overlay will appear in the top-right corner
 */

(function() {
    'use strict';
    
    // Check if overlay already exists
    if (document.getElementById('error-test-overlay')) {
        console.log('🧪 Error test overlay already exists');
        return;
    }
    
    console.log('🧪 Injecting Video Recorder Error Test Interface...');
    
    // Create and inject the overlay HTML
    const overlayHTML = `
    <div id="error-test-overlay" style="position: fixed; top: 20px; right: 20px; width: 350px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: 2px solid #4a5568; border-radius: 12px; box-shadow: 0 10px 25px rgba(0,0,0,0.3); z-index: 9999; font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; color: white; user-select: none; transition: all 0.3s ease;">
        <div class="overlay-header" id="overlay-header" style="background: rgba(0,0,0,0.2); padding: 12px 15px; border-radius: 10px 10px 0 0; cursor: move; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid rgba(255,255,255,0.1);">
            <h3 class="overlay-title" style="font-weight: bold; font-size: 14px; margin: 0;">🧪 Error Test Interface</h3>
            <div class="overlay-controls" style="display: flex; gap: 8px;">
                <button class="control-btn" id="toggle-btn" title="Collapse/Expand" style="background: rgba(255,255,255,0.2); border: none; color: white; width: 24px; height: 24px; border-radius: 4px; cursor: pointer; font-size: 12px; display: flex; align-items: center; justify-content: center;">−</button>
                <button class="control-btn" id="close-btn" title="Close" style="background: rgba(255,255,255,0.2); border: none; color: white; width: 24px; height: 24px; border-radius: 4px; cursor: pointer; font-size: 12px; display: flex; align-items: center; justify-content: center;">×</button>
            </div>
        </div>
        
        <div class="overlay-content" id="overlay-content" style="padding: 15px; max-height: 500px; overflow-y: auto;">
            <!-- Extension Status Section -->
            <div class="section" style="margin-bottom: 15px; background: rgba(255,255,255,0.1); padding: 12px; border-radius: 8px;">
                <div class="section-title" style="font-weight: bold; font-size: 13px; margin-bottom: 8px; color: #e2e8f0;">📊 Extension Status</div>
                <div class="status-display" id="extension-status" style="background: rgba(0,0,0,0.3); padding: 8px; border-radius: 4px; font-size: 11px; font-family: monospace; margin-bottom: 8px; max-height: 100px; overflow-y: auto;">
                    <div class="status-item">Loading status...</div>
                </div>
                <button onclick="window.errorTestInterface.refreshStatus()" style="width: 100%; padding: 8px; border: 1px solid rgba(255,255,255,0.2); border-radius: 6px; background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: white; font-size: 12px; cursor: pointer; font-weight: bold; margin-bottom: 5px;">🔄 Refresh Status</button>
            </div>

            <!-- Error Simulation Section -->
            <div class="section" style="margin-bottom: 15px; background: rgba(255,255,255,0.1); padding: 12px; border-radius: 8px;">
                <div class="section-title" style="font-weight: bold; font-size: 13px; margin-bottom: 8px; color: #e2e8f0;">⚠️ Error Simulation</div>
                <div class="form-group" style="margin-bottom: 10px;">
                    <label for="error-type" style="display: block; font-size: 12px; margin-bottom: 4px; color: #cbd5e0;">Error Type:</label>
                    <select id="error-type" style="width: 100%; padding: 8px; border: 1px solid rgba(255,255,255,0.2); border-radius: 6px; background: rgba(255,255,255,0.1); color: white; font-size: 12px; cursor: pointer;">
                        <option value="invalid_blob_url" style="background: #4a5568; color: white;">Invalid Blob URL</option>
                        <option value="no_video_element" style="background: #4a5568; color: white;">No Video Element</option>
                        <option value="no_video_source" style="background: #4a5568; color: white;">No Video Source</option>
                        <option value="mediarecorder_error" style="background: #4a5568; color: white;">MediaRecorder Error</option>
                        <option value="stream_capture_error" style="background: #4a5568; color: white;">Stream Capture Error</option>
                        <option value="video_playback_error" style="background: #4a5568; color: white;">Video Playback Error</option>
                        <option value="video_not_ready" style="background: #4a5568; color: white;">Video Not Ready</option>
                        <option value="no_duration" style="background: #4a5568; color: white;">No Duration</option>
                        <option value="video_error" style="background: #4a5568; color: white;">Video Element Error</option>
                    </select>
                </div>
                <button onclick="window.errorTestInterface.simulateError()" style="width: 100%; padding: 8px; border: none; border-radius: 6px; background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); color: white; font-size: 12px; cursor: pointer; font-weight: bold; margin-bottom: 5px;">🚨 Trigger Error</button>
                <button onclick="window.errorTestInterface.simulateRecovery()" style="width: 100%; padding: 8px; border: none; border-radius: 6px; background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%); color: white; font-size: 12px; cursor: pointer; font-weight: bold; margin-bottom: 5px;">🔄 Test Recovery</button>
            </div>

            <!-- Recovery State Section -->
            <div class="section" style="margin-bottom: 15px; background: rgba(255,255,255,0.1); padding: 12px; border-radius: 8px;">
                <div class="section-title" style="font-weight: bold; font-size: 13px; margin-bottom: 8px; color: #e2e8f0;">💾 Recovery State</div>
                <div class="status-display" id="recovery-status" style="background: rgba(0,0,0,0.3); padding: 8px; border-radius: 4px; font-size: 11px; font-family: monospace; margin-bottom: 8px; max-height: 100px; overflow-y: auto;">
                    <div class="status-item">No recovery state</div>
                </div>
                <button onclick="window.errorTestInterface.clearRecoveryState()" style="width: 100%; padding: 8px; border: none; border-radius: 6px; background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%); color: white; font-size: 12px; cursor: pointer; font-weight: bold; margin-bottom: 5px;">🧹 Clear Recovery</button>
                <button onclick="window.errorTestInterface.forcePageRefresh()" style="width: 100%; padding: 8px; border: none; border-radius: 6px; background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); color: white; font-size: 12px; cursor: pointer; font-weight: bold; margin-bottom: 5px;">🔄 Force Refresh</button>
            </div>

            <!-- Activity Log Section -->
            <div class="section" style="margin-bottom: 15px; background: rgba(255,255,255,0.1); padding: 12px; border-radius: 8px;">
                <div class="section-title" style="font-weight: bold; font-size: 13px; margin-bottom: 8px; color: #e2e8f0;">📝 Activity Log</div>
                <div class="log-display" id="activity-log" style="background: rgba(0,0,0,0.4); padding: 8px; border-radius: 4px; font-size: 10px; font-family: monospace; max-height: 120px; overflow-y: auto; border: 1px solid rgba(255,255,255,0.1);">
                    <div class="log-entry" style="margin-bottom: 2px; padding: 1px 0; color: #63b3ed;">Test interface injected via console</div>
                </div>
                <button onclick="window.errorTestInterface.clearLog()" style="width: 100%; padding: 8px; border: 1px solid rgba(255,255,255,0.2); border-radius: 6px; background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: white; font-size: 12px; cursor: pointer; font-weight: bold; margin-bottom: 5px;">🗑️ Clear Log</button>
            </div>
        </div>
    </div>
    `;
    
    // Inject the HTML
    document.body.insertAdjacentHTML('beforeend', overlayHTML);
    
    // Add the JavaScript functionality
    const script = document.createElement('script');
    script.textContent = `
        // Global variables for the injected overlay
        let isDragging = false;
        let currentX = 0;
        let currentY = 0;
        let initialX = 0;
        let initialY = 0;
        let isCollapsed = false;

        // Initialize the overlay
        function initializeInjectedOverlay() {
            const overlay = document.getElementById('error-test-overlay');
            const header = document.getElementById('overlay-header');
            const toggleBtn = document.getElementById('toggle-btn');
            const closeBtn = document.getElementById('close-btn');

            // Make draggable
            header.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);

            // Toggle collapse/expand
            toggleBtn.addEventListener('click', toggleCollapse);

            // Close overlay
            closeBtn.addEventListener('click', closeOverlay);

            // Prevent text selection during drag
            header.addEventListener('selectstart', e => e.preventDefault());
        }

        // Dragging functionality
        function dragStart(e) {
            const overlay = document.getElementById('error-test-overlay');
            const rect = overlay.getBoundingClientRect();
            
            initialX = e.clientX - rect.left;
            initialY = e.clientY - rect.top;
            
            if (e.target === document.getElementById('overlay-header') || 
                e.target.classList.contains('overlay-title')) {
                isDragging = true;
                overlay.style.cursor = 'grabbing';
            }
        }

        function drag(e) {
            if (isDragging) {
                e.preventDefault();
                const overlay = document.getElementById('error-test-overlay');
                
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                
                // Keep overlay within viewport
                const maxX = window.innerWidth - overlay.offsetWidth;
                const maxY = window.innerHeight - overlay.offsetHeight;
                
                currentX = Math.max(0, Math.min(currentX, maxX));
                currentY = Math.max(0, Math.min(currentY, maxY));
                
                overlay.style.left = currentX + 'px';
                overlay.style.top = currentY + 'px';
                overlay.style.right = 'auto';
            }
        }

        function dragEnd() {
            if (isDragging) {
                isDragging = false;
                const overlay = document.getElementById('error-test-overlay');
                overlay.style.cursor = 'default';
            }
        }

        // Toggle collapse/expand
        function toggleCollapse() {
            const overlay = document.getElementById('error-test-overlay');
            const toggleBtn = document.getElementById('toggle-btn');
            
            isCollapsed = !isCollapsed;
            
            if (isCollapsed) {
                overlay.style.height = '50px';
                overlay.style.overflow = 'hidden';
                toggleBtn.textContent = '+';
                toggleBtn.title = 'Expand';
            } else {
                overlay.style.height = 'auto';
                overlay.style.overflow = 'visible';
                toggleBtn.textContent = '−';
                toggleBtn.title = 'Collapse';
            }
        }

        // Close overlay
        function closeOverlay() {
            const overlay = document.getElementById('error-test-overlay');
            overlay.style.display = 'none';
            logActivity('Test interface closed', 'info');
        }

        // Refresh extension status
        function refreshStatus() {
            const statusDiv = document.getElementById('extension-status');
            const recoveryDiv = document.getElementById('recovery-status');

            // Check if content script is available
            if (typeof window.isProcessingBatch !== 'undefined') {
                updateExtensionStatus(statusDiv);
                updateRecoveryStatus(recoveryDiv);
            } else {
                statusDiv.innerHTML = '<div style="margin-bottom: 4px; padding: 2px 4px; border-radius: 2px; background: rgba(245, 101, 101, 0.3);">Extension not detected</div>';
                logActivity('Extension content script not found', 'error');
            }
        }

        // Update extension status display
        function updateExtensionStatus(statusDiv) {
            const batchStatus = window.isProcessingBatch ? 'Active' : 'Inactive';
            const batchClass = window.isProcessingBatch ? 'rgba(72, 187, 120, 0.3)' : 'rgba(245, 101, 101, 0.3)';

            let html = \`<div style="margin-bottom: 4px; padding: 2px 4px; border-radius: 2px; background: \${batchClass};">Batch Processing: \${batchStatus}</div>\`;

            // Check for video element
            const videoElement = document.querySelector('video');
            const videoStatus = videoElement ? 'Found' : 'Not Found';
            const videoClass = videoElement ? 'rgba(72, 187, 120, 0.3)' : 'rgba(245, 101, 101, 0.3)';
            html += \`<div style="margin-bottom: 4px; padding: 2px 4px; border-radius: 2px; background: \${videoClass};">Video Element: \${videoStatus}</div>\`;

            // Check recording state
            if (typeof window.videoRecorderControl !== 'undefined' && window.videoRecorderControl) {
                html += '<div style="margin-bottom: 4px; padding: 2px 4px; border-radius: 2px; background: rgba(72, 187, 120, 0.3);">Recorder: Active</div>';
            } else {
                html += '<div style="margin-bottom: 4px; padding: 2px 4px; border-radius: 2px; background: rgba(245, 101, 101, 0.3);">Recorder: Inactive</div>';
            }

            statusDiv.innerHTML = html;
            logActivity('Extension status refreshed', 'info');
        }

        // Update recovery status display
        function updateRecoveryStatus(recoveryDiv) {
            // Check Chrome storage for recovery state
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.local.get(['refreshPending', 'recoveryBatchIndex', 'recoveryRetryCount', 'batchItems'], function(result) {
                    let html = '';

                    if (result.refreshPending) {
                        html += '<div style="margin-bottom: 4px; padding: 2px 4px; border-radius: 2px; background: rgba(237, 137, 54, 0.3);">Recovery Pending: Yes</div>';
                        html += \`<div style="margin-bottom: 4px; padding: 2px 4px; border-radius: 2px; background: rgba(237, 137, 54, 0.3);">Batch Index: \${result.recoveryBatchIndex || 0}</div>\`;
                        html += \`<div style="margin-bottom: 4px; padding: 2px 4px; border-radius: 2px; background: rgba(237, 137, 54, 0.3);">Retry Count: \${result.recoveryRetryCount || 0}</div>\`;
                        html += \`<div style="margin-bottom: 4px; padding: 2px 4px; border-radius: 2px; background: rgba(237, 137, 54, 0.3);">Batch Items: \${result.batchItems ? result.batchItems.length : 0}</div>\`;
                    } else {
                        html += '<div style="margin-bottom: 4px; padding: 2px 4px; border-radius: 2px; background: rgba(245, 101, 101, 0.3);">Recovery Pending: No</div>';
                    }

                    recoveryDiv.innerHTML = html;
                });
            } else {
                recoveryDiv.innerHTML = '<div style="margin-bottom: 4px; padding: 2px 4px; border-radius: 2px; background: rgba(245, 101, 101, 0.3);">Chrome storage not available</div>';
            }
        }

        // Simulate error function
        function simulateError() {
            const errorType = document.getElementById('error-type').value;

            logActivity(\`Simulating error: \${errorType}\`, 'warning');

            // Check if extension is available
            if (typeof window.handleRecordingFailure === 'undefined') {
                logActivity('Error: handleRecordingFailure function not found', 'error');
                alert('Extension content script not loaded or error handling functions not available');
                return;
            }

            // Check if batch processing is active
            if (!window.isProcessingBatch) {
                logActivity('Warning: Batch processing not active', 'warning');
                if (!confirm('Batch processing is not active. Simulate error anyway?')) {
                    return;
                }
            }

            // Get current batch index
            let currentBatchIndex = 0;
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.local.get(['batchState'], function(result) {
                    currentBatchIndex = result.batchState?.currentBatchIndex || 0;

                    // Trigger the error handling
                    try {
                        const success = window.handleRecordingFailure(errorType, currentBatchIndex, 0);

                        if (success) {
                            logActivity(\`Error simulation triggered successfully: \${errorType}\`, 'success');
                            logActivity('Page refresh should occur in 2 seconds...', 'info');
                        } else {
                            logActivity(\`Error simulation completed (no recovery needed): \${errorType}\`, 'info');
                        }
                    } catch (error) {
                        logActivity(\`Error during simulation: \${error.message}\`, 'error');
                    }
                });
            } else {
                // Fallback without storage
                try {
                    const success = window.handleRecordingFailure(errorType, currentBatchIndex, 0);
                    logActivity(\`Error simulation result: \${success}\`, success ? 'success' : 'info');
                } catch (error) {
                    logActivity(\`Error during simulation: \${error.message}\`, 'error');
                }
            }
        }

        // Test recovery mechanism
        function simulateRecovery() {
            logActivity('Testing recovery mechanism...', 'info');

            // Check if we can access Chrome storage
            if (typeof chrome === 'undefined' || !chrome.storage) {
                logActivity('Error: Chrome storage not available', 'error');
                return;
            }

            // Create fake recovery state
            const fakeRecoveryState = {
                refreshPending: true,
                recoveryBatchIndex: 1,
                recoveryRetryCount: 0,
                recoveryTimestamp: Date.now(),
                batchItems: [
                    {
                        sectionIndex: 0,
                        itemIndex: 0,
                        sectionTitle: 'Test Section',
                        itemTitle: 'Test Video 1',
                        itemType: 'video'
                    },
                    {
                        sectionIndex: 0,
                        itemIndex: 1,
                        sectionTitle: 'Test Section',
                        itemTitle: 'Test Video 2',
                        itemType: 'video'
                    }
                ],
                recordingOptions: {
                    codec: 'vp9',
                    quality: 'high',
                    autoStop: true,
                    autoDownload: true
                }
            };

            // Store fake recovery state
            chrome.storage.local.set(fakeRecoveryState, function() {
                logActivity('Fake recovery state created', 'success');
                logActivity('Refreshing page to test recovery...', 'info');

                // Refresh the page after a short delay
                setTimeout(() => {
                    location.reload();
                }, 2000);
            });
        }

        // Clear recovery state
        function clearRecoveryState() {
            if (typeof chrome === 'undefined' || !chrome.storage) {
                logActivity('Error: Chrome storage not available', 'error');
                return;
            }

            chrome.storage.local.remove(['refreshPending', 'recoveryBatchIndex', 'recoveryRetryCount', 'recoveryTimestamp'], function() {
                logActivity('Recovery state cleared', 'success');
                refreshStatus();
            });
        }

        // Force page refresh
        function forcePageRefresh() {
            logActivity('Forcing page refresh...', 'warning');

            if (confirm('This will refresh the page. Continue?')) {
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        }

        // Log activity function
        function logActivity(message, type = 'info') {
            const logDiv = document.getElementById('activity-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');

            const colors = {
                error: '#f56565',
                success: '#48bb78',
                info: '#63b3ed',
                warning: '#ed8936'
            };

            logEntry.style.marginBottom = '2px';
            logEntry.style.padding = '1px 0';
            logEntry.style.color = colors[type] || colors.info;
            logEntry.textContent = \`[\${timestamp}] \${message}\`;

            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;

            // Keep only last 50 entries
            while (logDiv.children.length > 50) {
                logDiv.removeChild(logDiv.firstChild);
            }
        }

        // Clear log
        function clearLog() {
            const logDiv = document.getElementById('activity-log');
            logDiv.innerHTML = '<div style="margin-bottom: 2px; padding: 1px 0; color: #63b3ed;">Log cleared</div>';
        }

        // Export functions to global scope
        window.errorTestInterface = {
            simulateError,
            simulateRecovery,
            clearRecoveryState,
            forcePageRefresh,
            refreshStatus,
            logActivity,
            clearLog
        };

        // Initialize and start
        initializeInjectedOverlay();
        refreshStatus();
        logActivity('Error test interface fully loaded', 'success');
        logActivity('Keyboard shortcuts: Ctrl+Shift+T (toggle), Ctrl+Shift+E (error), Ctrl+Shift+R (recovery)', 'info');
    `;

    document.head.appendChild(script);
    
    // Initialize the overlay after a short delay
    setTimeout(() => {
        if (typeof initializeInjectedOverlay === 'function') {
            initializeInjectedOverlay();
        }
    }, 100);
    
    console.log('✅ Error test overlay injected successfully!');
    console.log('📋 Available keyboard shortcuts:');
    console.log('   Ctrl+Shift+T - Toggle overlay');
    console.log('   Ctrl+Shift+E - Trigger error simulation');
    console.log('   Ctrl+Shift+R - Test recovery mechanism');
    console.log('🔧 Access functions via: window.errorTestInterface');
    
})();
