// Platform detection and path utilities
let currentOS = 'unknown';

// Get path separator based on OS
function getPathSeparator(os) {
  return os === 'windows' ? '\\' : '/';
}

// Normalize path to use consistent separators based on OS
function normalizePath(path, os) {
  const separator = getPathSeparator(os);
  // First convert all separators to forward slashes (standard in JavaScript)
  let normalizedPath = path.replace(/\\/g, '/');
  
  // Then convert to the appropriate format for the OS
  if (os === 'windows') {
    normalizedPath = normalizedPath.replace(/\//g, '\\');
  }
  
  return normalizedPath;
}

// Join path segments with the appropriate separator for the OS
function joinPaths(os, ...segments) {
  const separator = getPathSeparator(os);
  
  // Filter out empty segments and normalize each segment
  const normalizedSegments = segments
    .filter(segment => segment && segment.trim() !== '')
    .map(segment => {
      // Remove leading/trailing separators
      let normalized = segment.trim();
      if (normalized.startsWith('/') || normalized.startsWith('\\')) {
        normalized = normalized.substring(1);
      }
      if (normalized.endsWith('/') || normalized.endsWith('\\')) {
        normalized = normalized.substring(0, normalized.length - 1);
      }
      return normalized;
    });
  
  // Join with the appropriate separator
  return normalizedSegments.join(separator);
}

// Listen for extension icon click
chrome.action.onClicked.addListener((tab) => {
  // This won't actually run since we have a popup defined,
  // but keeping it as a fallback
  chrome.scripting.executeScript({
    target: { tabId: tab.id },
    files: ['content.js']
  });
});

// Listen for messages from popup or content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log("🔄 Background script received message:", request.action, request);

  // Forward messages between popup and content script if needed
  if (request.action === 'recordingStopped') {
    console.log("🛑 Recording stopped message received in background script",
               "isBatch:", request.isBatch,
               "autoStopTriggered:", request.autoStopTriggered,
               "forceNextVideo:", request.forceNextVideo);

    // Print the complete request object for debugging
    console.log("📋 COMPLETE RECORDINGSTOPPED REQUEST:", JSON.stringify(request, null, 2));

    // Store batch state in case popup is closed
    if (request.isBatch || request.forceNextVideo) {
      console.log("📊 RECEIVED REQUEST WITH BATCH INFO:", {
        isBatch: request.isBatch,
        forceNextVideo: request.forceNextVideo,
        currentBatchIndex: request.currentBatchIndex,
        autoStopTriggered: request.autoStopTriggered
      });

      // Store the current batch state
      chrome.storage.local.get(['batchState', 'batchItems'], function(result) {
        console.log("📊 CURRENT BATCH STATE BEFORE UPDATE:", JSON.stringify(result.batchState, null, 2));
        console.log("📋 CURRENT BATCH ITEMS:", JSON.stringify(result.batchItems, null, 2));

        let batchState = result.batchState || {};

        // Update batch state with current item info
        if (request.currentBatchIndex !== undefined) {
          console.log(`📊 Using currentBatchIndex from request: ${request.currentBatchIndex}`);
          batchState.currentBatchIndex = request.currentBatchIndex;
        } else if (batchState.currentBatchIndex !== undefined) {
          console.log(`📊 [FIRST INCREMENT] Incrementing existing currentBatchIndex: ${batchState.currentBatchIndex} -> ${batchState.currentBatchIndex + 1}`);
          console.log(`📊 [FIRST INCREMENT] This is the ONLY place where we should increment the index`);
          batchState.currentBatchIndex++;
        } else {
          console.log(`📊 No currentBatchIndex found, setting to 0`);
          batchState.currentBatchIndex = 0;
        }

        console.log("📊 UPDATED BATCH STATE:", JSON.stringify(batchState, null, 2));
        chrome.storage.local.set({batchState: batchState});
      });
    }

    // Always handle batch processing in the background script
    // This ensures consistent behavior regardless of popup state
    if (request.isBatch || request.forceNextVideo) {
      console.log("🔄 Handling batch processing in background script");

      // Wait 5 seconds before moving to next video (simulating countdown)
      setTimeout(() => {
        console.log("⏱️ Background countdown complete, proceeding to next video");

        // Get the sender tab ID
        const tabId = sender.tab.id;

        // Get batch state to determine next item
        chrome.storage.local.get(['batchState', 'batchItems', 'recordingOptions', 'currentOS'], function(result) {
          // Update OS information if available
          if (result.currentOS) {
            currentOS = result.currentOS;
            console.log(`🖥️ Updated OS information from storage: ${currentOS}`);
          }
          // Print detailed information about what's in storage
          console.log("📊 BATCH STATE FROM STORAGE:", JSON.stringify(result.batchState, null, 2));
          console.log("📋 BATCH ITEMS FROM STORAGE:", JSON.stringify(result.batchItems, null, 2));
          console.log("⚙️ RECORDING OPTIONS FROM STORAGE:", JSON.stringify(result.recordingOptions, null, 2));

          // Print the complete batch items array for debugging
          if (result.batchItems && result.batchItems.length > 0) {
            console.log("COMPLETE BATCH ITEMS LIST IN BACKGROUND:");
            result.batchItems.forEach((item, index) => {
              console.log(`Item ${index}: ${item.itemTitle} (Section: ${item.sectionTitle}, SectionIndex: ${item.sectionIndex}, ItemIndex: ${item.itemIndex})`);
            });
          }

          let batchState = result.batchState || {};
          let batchItems = result.batchItems || [];
          let options = result.recordingOptions || {
            codec: 'vp9',
            quality: 'high',
            autoStop: true,
            autoDownload: true
          };

          // Get the current index from batch state
          const currentIndex = batchState.currentBatchIndex || 0;

          // Log the current index and batch items
          console.log("BACKGROUND: Current index =", currentIndex);

          // If we have a current item, log its details
          if (batchItems[currentIndex]) {
            console.log("BACKGROUND: Current item =", batchItems[currentIndex].itemTitle);
            console.log("BACKGROUND: Current item section =", batchItems[currentIndex].sectionTitle);
            console.log("BACKGROUND: Current item index =", batchItems[currentIndex].itemIndex);
          }

          // Calculate the next index (for later use)
          const nextIndex = currentIndex + 1;
          console.log("BATCH PROCESSING: Current index=" + currentIndex + ", Next index=" + nextIndex);
          console.log("BATCH ITEMS ARRAY:", JSON.stringify(batchItems, null, 2));

          console.log(`📊 CURRENT INDEX: ${currentIndex}, NEXT INDEX: ${nextIndex}`);

          // Log detailed information about each batch item
          console.log(`📋 DETAILED BATCH ITEMS (${batchItems.length} total):`);
          batchItems.forEach((item, idx) => {
            console.log(`   Item ${idx}: ${item.itemTitle}`);
            console.log(`      Section: ${item.sectionTitle}`);
            console.log(`      SectionIndex: ${item.sectionIndex}`);
            console.log(`      ItemIndex: ${item.itemIndex}`);
            console.log(`      ItemType: ${item.itemType}`);
          });

          // Check if the current item exists
          const currentItem = batchItems[currentIndex];
          if (!currentItem) {
            console.log(`❌ CURRENT ITEM NOT FOUND AT INDEX ${currentIndex}`);
            console.log("✅ No more items to process, ending batch processing");
            chrome.storage.local.remove(['batchState', 'batchItems']);
            return;
          }

          console.log(`📊 CURRENT ITEM: ${currentItem.itemTitle} (Section: ${currentItem.sectionTitle}, SectionIndex: ${currentItem.sectionIndex}, ItemIndex: ${currentItem.itemIndex})`);

          // Check if the next item exists
          const nextItem = batchItems[nextIndex];
          if (nextItem) {
            console.log(`📊 NEXT ITEM: ${nextItem.itemTitle} (Section: ${nextItem.sectionTitle}, SectionIndex: ${nextItem.sectionIndex}, ItemIndex: ${nextItem.itemIndex})`);
          } else {
            console.log(`❌ NEXT ITEM NOT FOUND AT INDEX ${nextIndex}`);
          }

          // [SECOND INCREMENT] This is where we were incrementing the index a second time
          console.log(`📊 [SECOND INCREMENT] Would have incremented index from ${currentIndex} to ${nextIndex}`);
          console.log(`📊 [SECOND INCREMENT] Current item: ${currentItem.itemTitle}, Next item would be: ${nextItem ? nextItem.itemTitle : 'none'}`);

          // IMPORTANT: We're NOT updating the batch state with the next index here
          // This prevents the index from being incremented twice
          // The index is already incremented in the recordingStopped message handler

          console.log(`📊 [SECOND INCREMENT REMOVED] NOT incrementing index again, keeping it at ${batchState.currentBatchIndex}`);

          console.log(`🎬 Processing current item: ${currentItem.itemTitle} (index ${currentIndex})`);

          // Also log all batch items for debugging
          console.log(`📋 All batch items:`, batchItems.map(item => item.itemTitle));

          // Log before sending message
          console.log("SENDING PROCESS VIDEO ITEM MESSAGE:");
          console.log("  currentItem:", JSON.stringify(currentItem, null, 2));
          console.log("  currentIndex:", currentIndex);
          console.log("  nextIndex:", nextIndex);
          console.log("  batchState:", JSON.stringify(batchState, null, 2));

          // Send message to content script to process current item
          chrome.tabs.sendMessage(tabId, {
            action: 'processVideoItem',
            item: currentItem,
            options: options,
            isBatch: true,
            os: currentOS  // Pass OS information to content script
          }, function(response) {
            console.log("Sent processVideoItem message to content script:", response);
          });

          // Check if we've reached the end of the batch
          if (nextIndex >= batchItems.length) {
            console.log("✅ This is the last item in the batch");
          }
        });
      }, 5000);
    }

    // Forward the message to the popup anyway (it will be ignored if popup is closed)
    chrome.runtime.sendMessage(request, function(response) {
      if (!chrome.runtime.lastError) {
        console.log('✅ Message also forwarded to popup:', response);
      }
    });

    // Store batch items and options if provided
    if (request.batchItems) {
      chrome.storage.local.set({batchItems: request.batchItems});
    }
    if (request.options) {
      chrome.storage.local.set({recordingOptions: request.options});
    }

    sendResponse({ received: true, from: 'background' });
  }
  // Handle download file request
  else if (request.action === 'downloadFile') {
    console.log('Download request received:', request.filename);
    
    // Update current OS from the request if available
    if (request.os) {
      currentOS = request.os;
      console.log(`🖥️ OS information received from content script: ${currentOS}`);
    }
    
    // Get the downloads directory from Chrome
    chrome.storage.local.get('downloadDirectory', function(data) {
      let downloadDirectory = data.downloadDirectory || '';

      // If we don't have a saved download directory, use the default downloads directory
      if (!downloadDirectory) {
        // Use Chrome's download API to download the file with directory structure
        // The filename should already be formatted correctly for the OS by content.js
        console.log(`📂 Using filename with directory structure: ${request.filename}`);
        
        chrome.downloads.download({
          url: request.url,
          filename: request.filename,
          saveAs: false
        }, function(downloadId) {
          if (chrome.runtime.lastError) {
            console.error('Download error:', chrome.runtime.lastError);
            sendResponse({
              success: false,
              error: chrome.runtime.lastError.message
            });
          } else {
            console.log('Download started with ID:', downloadId);

            // Get the download item to see where it was saved
            chrome.downloads.search({id: downloadId}, function(items) {
              if (items && items.length > 0) {
                // Store the directory for future use
                const downloadPath = items[0].filename;
                
                // Handle both Windows and macOS/Linux paths
                let lastSlashIndex = -1;
                if (currentOS === 'windows') {
                  lastSlashIndex = downloadPath.lastIndexOf('\\');
                } else {
                  lastSlashIndex = downloadPath.lastIndexOf('/');
                }
                
                if (lastSlashIndex > 0) {
                  const directory = downloadPath.substring(0, lastSlashIndex);
                  chrome.storage.local.set({
                    downloadDirectory: directory,
                    downloadOS: currentOS // Store the OS for future reference
                  });
                  console.log('Saved download directory:', directory);
                }
              }
            });

            sendResponse({
              success: true,
              downloadId: downloadId
            });
          }
        });
      } else {
        // We have a saved download directory, use it with the appropriate path separator
        
        // Get both download directory and OS information in one call
        chrome.storage.local.get(['downloadDirectory', 'downloadOS'], function(data) {
          const storedOS = data.downloadOS || currentOS;
          const directory = data.downloadDirectory || downloadDirectory;
          
          // Join paths using the appropriate separator for the OS
          const fullPath = joinPaths(storedOS, directory, request.filename);
          console.log(`📂 Using saved download directory with OS-specific path: ${fullPath}`);
          console.log(`🖥️ OS: ${storedOS}, Path separator: ${getPathSeparator(storedOS)}`);
          
          chrome.downloads.download({
            url: request.url,
            filename: fullPath,
            saveAs: false
          }, function(downloadId) {
            if (chrome.runtime.lastError) {
              console.error('Download error:', chrome.runtime.lastError);
              sendResponse({
                success: false,
                error: chrome.runtime.lastError.message
              });
            } else {
              console.log('Download started with ID:', downloadId);
              sendResponse({
                success: true,
                downloadId: downloadId
              });
            }
          });
        });
      }
    });

    // Return true to indicate we'll send a response asynchronously
    return true;
  }
  else {
    sendResponse({ received: true });
  }
});

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('Video Recorder Extension installed');

  // Detect OS from user agent (as a fallback)
  const userAgent = navigator.userAgent.toLowerCase();
  if (userAgent.indexOf('win') !== -1) currentOS = 'windows';
  else if (userAgent.indexOf('mac') !== -1) currentOS = 'macos';
  else if (userAgent.indexOf('linux') !== -1) currentOS = 'linux';
  
  console.log(`🖥️ Detected OS during initialization: ${currentOS}`);

  // Set default settings
  chrome.storage.sync.set({
    codec: 'vp9',
    quality: 'high',
    autoStop: true,
    autoDownload: true
  });
  
  // Store OS information
  chrome.storage.local.set({
    defaultOS: currentOS
  });
});
