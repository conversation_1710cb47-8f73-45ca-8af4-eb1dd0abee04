<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Recorder Error Test Interface</title>
    <style>
        /* Test Overlay Styles */
        #error-test-overlay {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 350px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: 2px solid #4a5568;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
            z-index: 9999;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
            user-select: none;
            transition: all 0.3s ease;
        }

        #error-test-overlay.collapsed {
            height: 50px;
            overflow: hidden;
        }

        .overlay-header {
            background: rgba(0,0,0,0.2);
            padding: 12px 15px;
            border-radius: 10px 10px 0 0;
            cursor: move;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .overlay-title {
            font-weight: bold;
            font-size: 14px;
            margin: 0;
        }

        .overlay-controls {
            display: flex;
            gap: 8px;
        }

        .control-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }

        .control-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .overlay-content {
            padding: 15px;
            max-height: 500px;
            overflow-y: auto;
        }

        .section {
            margin-bottom: 15px;
            background: rgba(255,255,255,0.1);
            padding: 12px;
            border-radius: 8px;
        }

        .section-title {
            font-weight: bold;
            font-size: 13px;
            margin-bottom: 8px;
            color: #e2e8f0;
        }

        .form-group {
            margin-bottom: 10px;
        }

        label {
            display: block;
            font-size: 12px;
            margin-bottom: 4px;
            color: #cbd5e0;
        }

        select, button {
            width: 100%;
            padding: 8px;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 6px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 12px;
            cursor: pointer;
        }

        select option {
            background: #4a5568;
            color: white;
        }

        button {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            border: none;
            font-weight: bold;
            transition: all 0.2s;
            margin-bottom: 5px;
        }

        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        button.danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        button.warning {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .status-display {
            background: rgba(0,0,0,0.3);
            padding: 8px;
            border-radius: 4px;
            font-size: 11px;
            font-family: monospace;
            margin-bottom: 8px;
            max-height: 100px;
            overflow-y: auto;
        }

        .status-item {
            margin-bottom: 4px;
            padding: 2px 4px;
            border-radius: 2px;
        }

        .status-active { background: rgba(72, 187, 120, 0.3); }
        .status-inactive { background: rgba(245, 101, 101, 0.3); }
        .status-pending { background: rgba(237, 137, 54, 0.3); }

        .log-display {
            background: rgba(0,0,0,0.4);
            padding: 8px;
            border-radius: 4px;
            font-size: 10px;
            font-family: monospace;
            max-height: 120px;
            overflow-y: auto;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .log-entry {
            margin-bottom: 2px;
            padding: 1px 0;
        }

        .log-error { color: #f56565; }
        .log-success { color: #48bb78; }
        .log-info { color: #63b3ed; }
        .log-warning { color: #ed8936; }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }
    </style>
</head>
<body>
    <div id="error-test-overlay">
        <div class="overlay-header" id="overlay-header">
            <h3 class="overlay-title">🧪 Error Test Interface</h3>
            <div class="overlay-controls">
                <button class="control-btn" id="toggle-btn" title="Collapse/Expand">−</button>
                <button class="control-btn" id="close-btn" title="Close">×</button>
            </div>
        </div>
        
        <div class="overlay-content" id="overlay-content">
            <!-- Extension Status Section -->
            <div class="section">
                <div class="section-title">📊 Extension Status</div>
                <div class="status-display" id="extension-status">
                    <div class="status-item">Loading status...</div>
                </div>
                <button onclick="refreshStatus()">🔄 Refresh Status</button>
            </div>

            <!-- Error Simulation Section -->
            <div class="section">
                <div class="section-title">⚠️ Error Simulation</div>
                <div class="form-group">
                    <label for="error-type">Error Type:</label>
                    <select id="error-type">
                        <option value="invalid_blob_url">Invalid Blob URL</option>
                        <option value="no_video_element">No Video Element</option>
                        <option value="no_video_source">No Video Source</option>
                        <option value="mediarecorder_error">MediaRecorder Error</option>
                        <option value="stream_capture_error">Stream Capture Error</option>
                        <option value="video_playback_error">Video Playback Error</option>
                        <option value="video_not_ready">Video Not Ready</option>
                        <option value="no_duration">No Duration</option>
                        <option value="video_error">Video Element Error</option>
                    </select>
                </div>
                <button onclick="simulateError()" class="danger">🚨 Trigger Error</button>
                <button onclick="simulateRecovery()" class="warning">🔄 Test Recovery</button>
            </div>

            <!-- Recovery State Section -->
            <div class="section">
                <div class="section-title">💾 Recovery State</div>
                <div class="status-display" id="recovery-status">
                    <div class="status-item">No recovery state</div>
                </div>
                <button onclick="clearRecoveryState()" class="warning">🧹 Clear Recovery</button>
                <button onclick="forcePageRefresh()" class="danger">🔄 Force Refresh</button>
            </div>

            <!-- Activity Log Section -->
            <div class="section">
                <div class="section-title">📝 Activity Log</div>
                <div class="log-display" id="activity-log">
                    <div class="log-entry log-info">Test interface loaded</div>
                </div>
                <button onclick="clearLog()">🗑️ Clear Log</button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let isDragging = false;
        let currentX = 0;
        let currentY = 0;
        let initialX = 0;
        let initialY = 0;
        let isCollapsed = false;

        // Initialize the overlay
        document.addEventListener('DOMContentLoaded', function() {
            initializeOverlay();
            refreshStatus();
            logActivity('Test interface initialized', 'success');
        });

        // Initialize overlay functionality
        function initializeOverlay() {
            const overlay = document.getElementById('error-test-overlay');
            const header = document.getElementById('overlay-header');
            const toggleBtn = document.getElementById('toggle-btn');
            const closeBtn = document.getElementById('close-btn');

            // Make draggable
            header.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);

            // Toggle collapse/expand
            toggleBtn.addEventListener('click', toggleCollapse);

            // Close overlay
            closeBtn.addEventListener('click', closeOverlay);

            // Prevent text selection during drag
            header.addEventListener('selectstart', e => e.preventDefault());
        }

        // Dragging functionality
        function dragStart(e) {
            const overlay = document.getElementById('error-test-overlay');
            const rect = overlay.getBoundingClientRect();

            initialX = e.clientX - rect.left;
            initialY = e.clientY - rect.top;

            if (e.target === document.getElementById('overlay-header') ||
                e.target.classList.contains('overlay-title')) {
                isDragging = true;
                overlay.style.cursor = 'grabbing';
            }
        }

        function drag(e) {
            if (isDragging) {
                e.preventDefault();
                const overlay = document.getElementById('error-test-overlay');

                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                // Keep overlay within viewport
                const maxX = window.innerWidth - overlay.offsetWidth;
                const maxY = window.innerHeight - overlay.offsetHeight;

                currentX = Math.max(0, Math.min(currentX, maxX));
                currentY = Math.max(0, Math.min(currentY, maxY));

                overlay.style.left = currentX + 'px';
                overlay.style.top = currentY + 'px';
                overlay.style.right = 'auto';
            }
        }

        function dragEnd() {
            if (isDragging) {
                isDragging = false;
                const overlay = document.getElementById('error-test-overlay');
                overlay.style.cursor = 'default';
            }
        }

        // Toggle collapse/expand
        function toggleCollapse() {
            const overlay = document.getElementById('error-test-overlay');
            const toggleBtn = document.getElementById('toggle-btn');

            isCollapsed = !isCollapsed;

            if (isCollapsed) {
                overlay.classList.add('collapsed');
                toggleBtn.textContent = '+';
                toggleBtn.title = 'Expand';
            } else {
                overlay.classList.remove('collapsed');
                toggleBtn.textContent = '−';
                toggleBtn.title = 'Collapse';
            }
        }

        // Close overlay
        function closeOverlay() {
            const overlay = document.getElementById('error-test-overlay');
            overlay.style.display = 'none';
            logActivity('Test interface closed', 'info');
        }

        // Refresh extension status
        function refreshStatus() {
            const statusDiv = document.getElementById('extension-status');
            const recoveryDiv = document.getElementById('recovery-status');

            // Check if content script is available
            if (typeof window.isProcessingBatch !== 'undefined') {
                updateExtensionStatus(statusDiv);
                updateRecoveryStatus(recoveryDiv);
            } else {
                statusDiv.innerHTML = '<div class="status-item status-inactive">Extension not detected</div>';
                logActivity('Extension content script not found', 'error');
            }
        }

        // Update extension status display
        function updateExtensionStatus(statusDiv) {
            const batchStatus = window.isProcessingBatch ? 'Active' : 'Inactive';
            const batchClass = window.isProcessingBatch ? 'status-active' : 'status-inactive';

            let html = `<div class="status-item ${batchClass}">Batch Processing: ${batchStatus}</div>`;

            // Check for video element
            const videoElement = document.querySelector('video');
            const videoStatus = videoElement ? 'Found' : 'Not Found';
            const videoClass = videoElement ? 'status-active' : 'status-inactive';
            html += `<div class="status-item ${videoClass}">Video Element: ${videoStatus}</div>`;

            // Check recording state
            if (typeof window.videoRecorderControl !== 'undefined' && window.videoRecorderControl) {
                html += '<div class="status-item status-active">Recorder: Active</div>';
            } else {
                html += '<div class="status-item status-inactive">Recorder: Inactive</div>';
            }

            statusDiv.innerHTML = html;
            logActivity('Extension status refreshed', 'info');
        }

        // Update recovery status display
        function updateRecoveryStatus(recoveryDiv) {
            // Check Chrome storage for recovery state
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.local.get(['refreshPending', 'recoveryBatchIndex', 'recoveryRetryCount', 'batchItems'], function(result) {
                    let html = '';

                    if (result.refreshPending) {
                        html += '<div class="status-item status-pending">Recovery Pending: Yes</div>';
                        html += `<div class="status-item status-pending">Batch Index: ${result.recoveryBatchIndex || 0}</div>`;
                        html += `<div class="status-item status-pending">Retry Count: ${result.recoveryRetryCount || 0}</div>`;
                        html += `<div class="status-item status-pending">Batch Items: ${result.batchItems ? result.batchItems.length : 0}</div>`;
                    } else {
                        html += '<div class="status-item status-inactive">Recovery Pending: No</div>';
                    }

                    recoveryDiv.innerHTML = html;
                });
            } else {
                recoveryDiv.innerHTML = '<div class="status-item status-inactive">Chrome storage not available</div>';
            }
        }

        // Simulate error function
        function simulateError() {
            const errorType = document.getElementById('error-type').value;

            logActivity(`Simulating error: ${errorType}`, 'warning');

            // Check if extension is available
            if (typeof window.handleRecordingFailure === 'undefined') {
                logActivity('Error: handleRecordingFailure function not found', 'error');
                alert('Extension content script not loaded or error handling functions not available');
                return;
            }

            // Check if batch processing is active
            if (!window.isProcessingBatch) {
                logActivity('Warning: Batch processing not active', 'warning');
                if (!confirm('Batch processing is not active. Simulate error anyway?')) {
                    return;
                }
            }

            // Get current batch index
            let currentBatchIndex = 0;
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.local.get(['batchState'], function(result) {
                    currentBatchIndex = result.batchState?.currentBatchIndex || 0;

                    // Trigger the error handling
                    try {
                        const success = window.handleRecordingFailure(errorType, currentBatchIndex, 0);

                        if (success) {
                            logActivity(`Error simulation triggered successfully: ${errorType}`, 'success');
                            logActivity('Page refresh should occur in 2 seconds...', 'info');
                        } else {
                            logActivity(`Error simulation completed (no recovery needed): ${errorType}`, 'info');
                        }
                    } catch (error) {
                        logActivity(`Error during simulation: ${error.message}`, 'error');
                    }
                });
            } else {
                // Fallback without storage
                try {
                    const success = window.handleRecordingFailure(errorType, currentBatchIndex, 0);
                    logActivity(`Error simulation result: ${success}`, success ? 'success' : 'info');
                } catch (error) {
                    logActivity(`Error during simulation: ${error.message}`, 'error');
                }
            }
        }

        // Test recovery mechanism
        function simulateRecovery() {
            logActivity('Testing recovery mechanism...', 'info');

            // Check if we can access Chrome storage
            if (typeof chrome === 'undefined' || !chrome.storage) {
                logActivity('Error: Chrome storage not available', 'error');
                return;
            }

            // Create fake recovery state
            const fakeRecoveryState = {
                refreshPending: true,
                recoveryBatchIndex: 1,
                recoveryRetryCount: 0,
                recoveryTimestamp: Date.now(),
                batchItems: [
                    {
                        sectionIndex: 0,
                        itemIndex: 0,
                        sectionTitle: 'Test Section',
                        itemTitle: 'Test Video 1',
                        itemType: 'video'
                    },
                    {
                        sectionIndex: 0,
                        itemIndex: 1,
                        sectionTitle: 'Test Section',
                        itemTitle: 'Test Video 2',
                        itemType: 'video'
                    }
                ],
                recordingOptions: {
                    codec: 'vp9',
                    quality: 'high',
                    autoStop: true,
                    autoDownload: true
                }
            };

            // Store fake recovery state
            chrome.storage.local.set(fakeRecoveryState, function() {
                logActivity('Fake recovery state created', 'success');
                logActivity('Refreshing page to test recovery...', 'info');

                // Refresh the page after a short delay
                setTimeout(() => {
                    location.reload();
                }, 2000);
            });
        }

        // Clear recovery state
        function clearRecoveryState() {
            if (typeof chrome === 'undefined' || !chrome.storage) {
                logActivity('Error: Chrome storage not available', 'error');
                return;
            }

            chrome.storage.local.remove(['refreshPending', 'recoveryBatchIndex', 'recoveryRetryCount', 'recoveryTimestamp'], function() {
                logActivity('Recovery state cleared', 'success');
                refreshStatus();
            });
        }

        // Force page refresh
        function forcePageRefresh() {
            logActivity('Forcing page refresh...', 'warning');

            if (confirm('This will refresh the page. Continue?')) {
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        }

        // Log activity function
        function logActivity(message, type = 'info') {
            const logDiv = document.getElementById('activity-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');

            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;

            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;

            // Keep only last 50 entries
            while (logDiv.children.length > 50) {
                logDiv.removeChild(logDiv.firstChild);
            }
        }

        // Clear log
        function clearLog() {
            const logDiv = document.getElementById('activity-log');
            logDiv.innerHTML = '<div class="log-entry log-info">Log cleared</div>';
        }

        // Advanced error simulation functions
        function simulateSpecificError(errorType, customBatchIndex = null) {
            logActivity(`Advanced simulation: ${errorType}`, 'warning');

            // Get batch index
            const batchIndex = customBatchIndex !== null ? customBatchIndex :
                (window.currentBatchIndex || 0);

            // Call the error handler directly
            if (typeof window.handleRecordingFailure !== 'undefined') {
                try {
                    const result = window.handleRecordingFailure(errorType, batchIndex, 0);
                    logActivity(`Error handler result: ${result}`, result ? 'success' : 'info');
                    return result;
                } catch (error) {
                    logActivity(`Error handler exception: ${error.message}`, 'error');
                    return false;
                }
            } else {
                logActivity('Error: handleRecordingFailure not available', 'error');
                return false;
            }
        }

        // Validate extension state
        function validateExtensionState() {
            logActivity('Validating extension state...', 'info');

            const checks = [
                { name: 'Content Script', check: () => typeof window.isProcessingBatch !== 'undefined' },
                { name: 'Error Handler', check: () => typeof window.handleRecordingFailure !== 'undefined' },
                { name: 'Recovery Functions', check: () => typeof window.storeRecoveryState !== 'undefined' },
                { name: 'Chrome Storage', check: () => typeof chrome !== 'undefined' && chrome.storage },
                { name: 'Video Element', check: () => document.querySelector('video') !== null },
                { name: 'Batch Processing', check: () => window.isProcessingBatch === true }
            ];

            let allPassed = true;
            checks.forEach(check => {
                const passed = check.check();
                const status = passed ? 'success' : 'error';
                logActivity(`${check.name}: ${passed ? 'PASS' : 'FAIL'}`, status);
                if (!passed) allPassed = false;
            });

            logActivity(`Overall validation: ${allPassed ? 'PASS' : 'FAIL'}`, allPassed ? 'success' : 'error');
            return allPassed;
        }

        // Auto-refresh status every 5 seconds
        setInterval(() => {
            if (!isCollapsed && document.getElementById('error-test-overlay').style.display !== 'none') {
                refreshStatus();
            }
        }, 5000);

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+Shift+T to toggle overlay
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                const overlay = document.getElementById('error-test-overlay');
                if (overlay.style.display === 'none') {
                    overlay.style.display = 'block';
                    logActivity('Test interface shown via keyboard shortcut', 'info');
                } else {
                    closeOverlay();
                }
            }

            // Ctrl+Shift+E to trigger error simulation
            if (e.ctrlKey && e.shiftKey && e.key === 'E') {
                e.preventDefault();
                simulateError();
            }

            // Ctrl+Shift+R to test recovery
            if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                e.preventDefault();
                simulateRecovery();
            }
        });

        // Export functions to global scope for console access
        window.errorTestInterface = {
            simulateError,
            simulateRecovery,
            clearRecoveryState,
            forcePageRefresh,
            validateExtensionState,
            simulateSpecificError,
            refreshStatus,
            logActivity
        };

        logActivity('Error test interface fully loaded', 'success');
        logActivity('Keyboard shortcuts: Ctrl+Shift+T (toggle), Ctrl+Shift+E (error), Ctrl+Shift+R (recovery)', 'info');
    </script>
</body>
</html>
