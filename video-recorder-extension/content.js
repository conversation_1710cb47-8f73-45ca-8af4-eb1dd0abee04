// Global variables to store recorder state
let videoRecorderControl = null;
let recordingOptions = null;
let recordingStatusUI = null;
let videoEndCheckInterval = null;
let currentRecordingStatus = '';
let courseStructure = null;
let currentDirectoryPath = null;
let currentSectionIndex = null;

// Recovery state variables
let isRecoveryMode = false;
let recoveryCheckCompleted = false;

// Function to store recovery state before page refresh
function storeRecoveryState(currentBatchIndex, retryCount = 0) {
  console.log(`📦 Storing recovery state: index=${currentBatchIndex}, retryCount=${retryCount}`);

  chrome.storage.local.set({
    refreshPending: true,
    recoveryBatchIndex: currentBatchIndex,
    recoveryRetryCount: retryCount,
    recoveryTimestamp: Date.now()
  }, function() {
    if (chrome.runtime.lastError) {
      console.error("❌ Error storing recovery state:", chrome.runtime.lastError);
    } else {
      console.log("✅ Recovery state stored successfully");
    }
  });
}

// Function to check and resume batch processing after page refresh
function checkAndResumeAfterRefresh() {
  if (recoveryCheckCompleted) return;
  recoveryCheckCompleted = true;

  console.log("🔍 Checking for pending batch recovery...");

  chrome.storage.local.get(['refreshPending', 'recoveryBatchIndex', 'recoveryRetryCount', 'recoveryTimestamp', 'batchItems', 'recordingOptions'], function(result) {
    if (chrome.runtime.lastError) {
      console.error("❌ Error checking recovery state:", chrome.runtime.lastError);
      return;
    }

    if (result.refreshPending && result.batchItems && result.batchItems.length > 0) {
      const recoveryIndex = result.recoveryBatchIndex || 0;
      const retryCount = result.recoveryRetryCount || 0;
      const timestamp = result.recoveryTimestamp || 0;

      // Check if recovery state is not too old (max 10 minutes)
      const maxAge = 10 * 60 * 1000; // 10 minutes
      if (Date.now() - timestamp > maxAge) {
        console.log("⚠️ Recovery state too old, clearing...");
        clearRecoveryState();
        return;
      }

      console.log(`🔄 Resuming batch processing from index ${recoveryIndex} (retry ${retryCount})`);
      isRecoveryMode = true;

      // Wait for page to fully load before resuming
      setTimeout(() => {
        resumeBatchProcessing(recoveryIndex, retryCount, result.batchItems, result.recordingOptions);
      }, 3000);
    } else {
      console.log("ℹ️ No pending batch recovery found");
    }
  });
}

// Function to clear recovery state
function clearRecoveryState() {
  console.log("🧹 Clearing recovery state");
  chrome.storage.local.remove(['refreshPending', 'recoveryBatchIndex', 'recoveryRetryCount', 'recoveryTimestamp'], function() {
    if (chrome.runtime.lastError) {
      console.error("❌ Error clearing recovery state:", chrome.runtime.lastError);
    } else {
      console.log("✅ Recovery state cleared");
    }
  });
}

// Function to resume batch processing after page refresh
function resumeBatchProcessing(recoveryIndex, retryCount, batchItems, recordingOptions) {
  console.log(`🔄 Resuming batch processing: index=${recoveryIndex}, retry=${retryCount}, total=${batchItems.length}`);

  // Validate recovery data
  if (!batchItems || batchItems.length === 0) {
    console.error("❌ No batch items found for recovery");
    clearRecoveryState();
    return;
  }

  if (recoveryIndex >= batchItems.length) {
    console.log("✅ All items processed, clearing recovery state");
    clearRecoveryState();
    return;
  }

  const currentItem = batchItems[recoveryIndex];
  console.log(`📋 Resuming with item: ${currentItem.itemTitle} (Section: ${currentItem.sectionTitle})`);

  // Set global flags for batch processing
  window.isProcessingBatch = true;
  currentSectionIndex = currentItem.sectionIndex;

  // Create directory structure if needed
  createDirectoryStructure(currentItem.sectionTitle, currentItem.sectionIndex);

  // Process the current item
  const itemElementResult = findItemElement(currentItem.sectionIndex, currentItem.itemIndex);

  // Handle both Promise and direct element return cases
  const processItem = (itemElement) => {
    if (!itemElement) {
      console.error(`❌ Could not find item element for ${currentItem.itemTitle} during recovery`);

      // If retry count is less than max, try page refresh again
      if (retryCount < 2) {
        console.log(`🔄 Retrying with page refresh (attempt ${retryCount + 1})`);
        storeRecoveryState(recoveryIndex, retryCount + 1);
        setTimeout(() => {
          location.reload();
        }, 2000);
        return;
      } else {
        console.error(`❌ Max retries exceeded for ${currentItem.itemTitle}, skipping to next item`);
        // Move to next item
        const nextIndex = recoveryIndex + 1;
        if (nextIndex < batchItems.length) {
          storeRecoveryState(nextIndex, 0);
          setTimeout(() => {
            location.reload();
          }, 2000);
        } else {
          console.log("✅ Batch processing completed");
          clearRecoveryState();
        }
        return;
      }
    }

    // Click on the item to navigate to it
    console.log(`🖱️ Clicking on item: ${currentItem.itemTitle}`);
    itemElement.click();

    // Wait for video to load and start recording
    setTimeout(() => {
      const videoElement = findVideoElement();

      if (videoElement) {
        // Start recording with the provided options
        recordingOptions = recordingOptions || {
          codec: 'vp9',
          quality: 'high',
          autoStop: true,
          autoDownload: true
        };

        const result = startRecording(recordingOptions, currentItem.sectionTitle, currentItem.itemTitle);

        if (result && result.success !== false) {
          console.log(`✅ Successfully resumed recording for ${currentItem.itemTitle}`);
          // Clear recovery state since we're now actively recording
          clearRecoveryState();
        } else {
          console.error(`❌ Failed to start recording for ${currentItem.itemTitle} during recovery`);
          // Try refresh again if retries available
          if (retryCount < 2) {
            storeRecoveryState(recoveryIndex, retryCount + 1);
            setTimeout(() => {
              location.reload();
            }, 2000);
          } else {
            // Skip to next item
            const nextIndex = recoveryIndex + 1;
            if (nextIndex < batchItems.length) {
              storeRecoveryState(nextIndex, 0);
              setTimeout(() => {
                location.reload();
              }, 2000);
            } else {
              clearRecoveryState();
            }
          }
        }
      } else {
        console.error(`❌ No video element found for ${currentItem.itemTitle} during recovery`);
        // Try refresh again if retries available
        if (retryCount < 2) {
          storeRecoveryState(recoveryIndex, retryCount + 1);
          setTimeout(() => {
            location.reload();
          }, 2000);
        } else {
          // Skip to next item
          const nextIndex = recoveryIndex + 1;
          if (nextIndex < batchItems.length) {
            storeRecoveryState(nextIndex, 0);
            setTimeout(() => {
              location.reload();
            }, 2000);
          } else {
            clearRecoveryState();
          }
        }
      }
    }, 3000); // Wait for page to fully load
  };

  // Check if itemElementResult is a Promise
  if (itemElementResult instanceof Promise) {
    itemElementResult.then(processItem);
  } else {
    processItem(itemElementResult);
  }
}

// Detect operating system
function detectOS() {
  const userAgent = navigator.userAgent.toLowerCase();
  if (userAgent.indexOf('win') !== -1) return 'windows';
  if (userAgent.indexOf('mac') !== -1) return 'macos';
  if (userAgent.indexOf('linux') !== -1) return 'linux';
  return 'unknown';
}

// Get the current operating system
let currentOS = detectOS();
console.log(`🖥️ Detected operating system: ${currentOS}`);

// Define path separator based on OS
let PATH_SEPARATOR = currentOS === 'windows' ? '\\' : '/';

// Handle start recording message
function handleStartRecording(options, sendResponse) {
  recordingOptions = options;
  const result = startRecording(recordingOptions);
  sendResponse(result);
}

// Handle pause recording message
function handlePauseRecording(sendResponse) {
  if (videoRecorderControl?.isPaused() === false) {
    const paused = videoRecorderControl.pause();
    sendResponse({ success: paused });
  } else {
    sendResponse({ success: false, error: 'No active recording or already paused' });
  }
}

// Handle resume recording message
function handleResumeRecording(sendResponse) {
  if (videoRecorderControl?.isPaused()) {
    const resumed = videoRecorderControl.resume();
    sendResponse({ success: resumed });
  } else {
    sendResponse({ success: false, error: 'No active recording or not paused' });
  }
}

// Handle stop recording message
function handleStopRecording(sendResponse) {
  console.log("🛑 handleStopRecording called, videoRecorderControl:", videoRecorderControl ? "exists" : "null");

  if (videoRecorderControl) {
    console.log("🎬 Stopping active recording");
    videoRecorderControl.stop();

    // Clear any existing intervals to prevent issues
    if (videoEndCheckInterval) {
      console.log("⏱️ Clearing existing video end check interval");
      clearInterval(videoEndCheckInterval);
      videoEndCheckInterval = null;
    }

    sendResponse({
      success: true,
      downloadStarted: window.autoDownloadAfterRecording,
      isBatch: window.isProcessingBatch
    });

    videoRecorderControl = null;
  } else {
    console.warn("⚠️ No active recording to stop");
    sendResponse({
      success: false,
      error: 'No active recording',
      isBatch: window.isProcessingBatch
    });
  }
}

// Handle get recording state message
function handleGetRecordingState(sendResponse) {
  if (videoRecorderControl) {
    sendResponse({
      isRecording: true,
      isPaused: videoRecorderControl.isPaused(),
      status: currentRecordingStatus
    });
  } else {
    sendResponse({
      isRecording: false
    });
  }
}

// Handle parse course structure message
function handleParseCourseStructure(sendResponse) {
  try {
    console.log("🔍 Parsing course structure...");

    // Get course title
    const courseTitle = getCourseTitle();
    console.log("📚 Course title:", courseTitle);

    // Parse course sections
    const sections = parseSections();

    // Create course structure object
    const courseStructure = {
      title: courseTitle,
      sections: sections
    };

    console.log("✅ Course structure parsed successfully:", courseStructure);
    sendResponse({ success: true, courseStructure: courseStructure });
  } catch (error) {
    console.error("❌ Error parsing course structure:", error);
    sendResponse({ success: false, error: error.message });
  }
}

// Handle process video item message
function handleProcessVideoItem(request, sendResponse) {
  try {
    const item = request.item;
    const options = request.options;
    const isBatch = request.isBatch || false;

    // Update OS information if provided
    if (request.os) {
      console.log(`🖥️ Updating OS information from background script: ${request.os}`);
      currentOS = request.os;
      // Update path separator based on new OS
      PATH_SEPARATOR = currentOS === 'windows' ? '\\' : '/';
    }

    console.log(`🎬 Processing video item: ${item.itemTitle}${isBatch ? ' (batch mode)' : ''}`);

    // Set global flags for batch processing
    window.isProcessingBatch = isBatch;

    // Log batch processing details
    if (isBatch) {
      console.log(`🔄 Batch processing: Processing item ${item.itemTitle}`);
      console.log(`📊 Item details: Section=${item.sectionTitle}, Index=${item.itemIndex}, SectionIndex=${item.sectionIndex}`);

      // Print the complete request object for debugging
      console.log("📋 COMPLETE REQUEST OBJECT:", JSON.stringify(request, null, 2));
    }

    // Store the section index for directory naming
    currentSectionIndex = item.sectionIndex;

    // Create directory structure if needed
    createDirectoryStructure(item.sectionTitle, item.sectionIndex);

    // Find and click on the item to navigate to it
    const itemElementResult = findItemElement(item.sectionIndex, item.itemIndex);

    // Handle both Promise and direct element return cases
    const processItem = (itemElement) => {
      if (!itemElement) {
        console.error(`❌ Could not find item element for ${item.itemTitle}`);
        sendResponse({ success: false, error: 'Item element not found' });
        return;
      }

      // Click on the item to navigate to it
      console.log(`🖱️ Clicking on item: ${item.itemTitle}`);
      itemElement.click();

      // Wait for video to load with retry mechanism
      let retryCount = 0;
      const maxRetries = 3;
      const checkAndStartRecording = () => {
        // Check if page has loaded and video is available
        const videoElement = findVideoElement();

        if (videoElement) {
          // Start recording with the provided options
          recordingOptions = options;
          const result = startRecording(recordingOptions, item.sectionTitle, item.itemTitle);

          if (result.success) {
            console.log(`✅ Started recording for ${item.itemTitle}`);
            sendResponse({ success: true });
          } else {
            console.error(`❌ Failed to start recording for ${item.itemTitle}:`, result.error);
            sendResponse({ success: false, error: result.error });
          }
        } else {
          // If we haven't exceeded max retries, try again
          if (retryCount < maxRetries) {
            retryCount++;
            console.log(`⏳ Video not found yet, retrying (${retryCount}/${maxRetries})...`);
            setTimeout(checkAndStartRecording, 2000); // Wait 2 seconds before retrying
          } else {
            // Max retries exceeded, send error response
            console.error(`❌ Failed to find video after ${maxRetries} attempts`);
            sendResponse({
              success: false,
              error: `Video not found after ${maxRetries} attempts. The page might not contain a video or it might be in a format that cannot be accessed.`
            });
          }
        }
      };

      // Start the check and retry process
      setTimeout(checkAndStartRecording, 2000); // Initial wait for page to load
    };

    // Check if itemElementResult is a Promise
    if (itemElementResult instanceof Promise) {
      itemElementResult.then(processItem);
    } else {
      processItem(itemElementResult);
    }

  } catch (error) {
    console.error("❌ Error processing video item:", error);
    sendResponse({ success: false, error: error.message });
  }

  // Return true to indicate we'll call sendResponse asynchronously
  return true;
}

// Initialize recovery check when content script loads
document.addEventListener('DOMContentLoaded', function() {
  console.log("📄 Content script DOM loaded, checking for recovery...");
  setTimeout(checkAndResumeAfterRefresh, 1000);
});

// Also check on window load as fallback
window.addEventListener('load', function() {
  console.log("🪟 Window loaded, checking for recovery...");
  setTimeout(checkAndResumeAfterRefresh, 1000);
});

// Listen for messages from the popup
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  console.log('Content script received message:', request);

  // Update OS information if provided in any message
  if (request.os) {
    console.log(`🖥️ Updating OS information from message: ${request.os}`);
    currentOS = request.os;
    // Update path separator based on new OS
    PATH_SEPARATOR = currentOS === 'windows' ? '\\' : '/';
  }

  try {
    // Route message to appropriate handler based on action
    switch (request.action) {
      case 'getVideoTitle':
        sendResponse({ title: getVideoTitle() });
        break;
      case 'startRecording':
        handleStartRecording(request.options, sendResponse);
        break;
      case 'pauseRecording':
        handlePauseRecording(sendResponse);
        break;
      case 'resumeRecording':
        handleResumeRecording(sendResponse);
        break;
      case 'stopRecording':
        handleStopRecording(sendResponse);
        break;
      case 'getRecordingState':
        handleGetRecordingState(sendResponse);
        break;
      case 'parseCourseStructure':
        handleParseCourseStructure(sendResponse);
        break;
      case 'processVideoItem':
        // This handler will call sendResponse asynchronously
        handleProcessVideoItem(request, sendResponse);
        return true; // Keep the message channel open for async response
      default:
        sendResponse({ success: false, error: 'Unknown action: ' + request.action });
    }
  } catch (error) {
    console.error('Error in content script:', error);
    sendResponse({ success: false, error: error.message });
  }

  // For synchronous handlers, we still need to return true to keep the message channel open
  return true;
});

// Function to get course title
function getCourseTitle() {
  try {
    // Try to find the course title using the selector from web_selectors.py
    const courseTitleElement = document.querySelector("div.column.col-5.hide-sm h3.text-ellipsis");

    if (courseTitleElement) {
      const title = courseTitleElement.textContent.trim();
      console.log("Found course title:", title);
      return title;
    }

    // Fallback to page title if course title element not found
    const pageTitle = document.title.split('|')[0].trim();
    console.log("Using page title as course title:", pageTitle);
    return pageTitle;
  } catch (error) {
    console.error("Error getting course title:", error);
    return "Course";
  }
}

// Function to parse course sections
function parseSections() {
  try {
    // Find all course sections using the selector from web_selectors.py
    const sectionElements = document.querySelectorAll("div.accordion.courseItem");
    console.log(`Found ${sectionElements.length} section elements`);

    const sections = [];

    // Process each section synchronously
    for (let index = 0; index < sectionElements.length; index++) {
      const sectionElement = sectionElements[index];

      try {
        // Get section title
        const titleElement = sectionElement.querySelector("div.title");
        const title = titleElement ? titleElement.textContent.trim() : `Section ${index + 1}`;

        // Get section ID
        const sectionId = sectionElement.getAttribute("data-id") || `section-${index}`;

        // Check if section is expanded, if not, expand it temporarily
        const sectionCheckboxId = `accordion-${sectionId}`;
        const checkbox = document.querySelector(`input#${sectionCheckboxId}`);
        let wasExpanded = true;

        if (checkbox && !checkbox.checked) {
          wasExpanded = false;
          // Temporarily expand the section to get items
          const toggle = sectionElement.querySelector("label.accordion-header");
          if (toggle) {
            toggle.click();
          }

          // Give it a moment to expand
          // Simple delay to allow the section to expand
          // In a real app, you might want to use MutationObserver instead
          const startTime = Date.now();
          while (Date.now() - startTime < 300) {
            // Busy wait to ensure synchronous execution
          }
        }

        // Find section items
        const itemElements = sectionElement.querySelectorAll("div.courseSubItem");
        const items = [];

        for (let itemIndex = 0; itemIndex < itemElements.length; itemIndex++) {
          const itemElement = itemElements[itemIndex];

          try {
            // Get item title
            const itemTitleElement = itemElement.querySelector("div.title");
            const itemTitle = itemTitleElement ? itemTitleElement.textContent.trim() : `Item ${itemIndex + 1}`;

            // Get item type and ID
            const itemType = itemElement.getAttribute("data-type") || "unknown";
            const itemId = itemElement.getAttribute("data-id") || `item-${itemIndex}`;

            items.push({
              title: itemTitle,
              type: itemType,
              id: itemId,
              index: itemIndex
            });
          } catch (itemError) {
            console.error(`Error parsing item ${itemIndex} in section ${title}:`, itemError);
          }
        }

        sections.push({
          title: title,
          id: sectionId,
          index: index,
          items: items
        });

        // Collapse section if it wasn't expanded before
        if (!wasExpanded) {
          const toggle = sectionElement.querySelector("label.accordion-header");
          if (toggle) {
            toggle.click();
          }
        }
      } catch (sectionError) {
        console.error(`Error parsing section ${index}:`, sectionError);
      }
    }

    return sections;
  } catch (error) {
    console.error("Error parsing sections:", error);
    return [];
  }
}

// Function to find an item element by section and item index
function findItemElement(sectionIndex, itemIndex) {
  try {
    // Find all section elements
    const sectionElements = document.querySelectorAll("div.accordion.courseItem");

    if (!sectionElements || sectionElements.length <= sectionIndex) {
      console.error(`Section with index ${sectionIndex} not found`);
      return null;
    }

    const sectionElement = sectionElements[sectionIndex];

    // Make sure section is expanded
    const sectionId = sectionElement.getAttribute("data-id") || `section-${sectionIndex}`;
    const sectionCheckboxId = `accordion-${sectionId}`;
    const checkbox = document.querySelector(`input#${sectionCheckboxId}`);

    if (checkbox && !checkbox.checked) {
      // Expand the section
      const toggle = sectionElement.querySelector("label.accordion-header");
      if (toggle) {
        toggle.click();
        // Wait for section to expand
        console.log(`Expanding section ${sectionIndex}`);
        return new Promise((resolve) => {
          setTimeout(() => {
            // Now find the item
            const itemElements = sectionElement.querySelectorAll("div.courseSubItem");

            if (!itemElements || itemElements.length <= itemIndex) {
              console.error(`Item with index ${itemIndex} not found in section ${sectionIndex}`);
              resolve(null);
              return;
            }

            resolve(itemElements[itemIndex]);
          }, 1000);
        });
      }
    }

    // Section is already expanded, find the item
    const itemElements = sectionElement.querySelectorAll("div.courseSubItem");

    if (!itemElements || itemElements.length <= itemIndex) {
      console.error(`Item with index ${itemIndex} not found in section ${sectionIndex}`);
      return null;
    }

    return itemElements[itemIndex];
  } catch (error) {
    console.error("Error finding item element:", error);
    return null;
  }
}

// Function to create directory structure
function createDirectoryStructure(sectionTitle, sectionIndex) {
  try {
    // Get course title
    const courseTitle = getCourseTitle();

    if (!courseTitle || !sectionTitle) {
      console.warn("Missing course title or section title for directory structure");
      // Use fallback values if missing
      const fallbackCourseTitle = courseTitle || "Course";
      const fallbackSectionTitle = sectionTitle || "Section";

      // Sanitize names for use as directory names
      const sanitizedCourseTitle = sanitizeFilename(fallbackCourseTitle);
      const sanitizedSectionTitle = sanitizeFilename(fallbackSectionTitle);

      // Create directory path - always use forward slashes for internal representation
      // This will be converted to the appropriate format when needed
      currentDirectoryPath = `${sanitizedCourseTitle}/${sanitizedSectionTitle}`;
      console.log(`📂 Using fallback directory path: ${currentDirectoryPath}`);

      return currentDirectoryPath;
    }

    // Sanitize names for use as directory names
    const sanitizedCourseTitle = sanitizeFilename(courseTitle);

    // Add numbering to section title if sectionIndex is provided
    let formattedSectionTitle = sectionTitle;
    if (typeof sectionIndex === 'number') {
      // Format section number with leading zero for single digits (01, 02, etc.)
      const sectionNumber = (sectionIndex + 1).toString().padStart(2, '0');
      formattedSectionTitle = `${sectionNumber}_${sectionTitle}`;
      console.log(`📂 Adding numbering to section: ${formattedSectionTitle}`);
    }

    const sanitizedSectionTitle = sanitizeFilename(formattedSectionTitle);

    // Create directory path - always use forward slashes for internal representation
    // This is the standard for paths in JavaScript and will be converted when needed
    currentDirectoryPath = `${sanitizedCourseTitle}/${sanitizedSectionTitle}`;
    console.log(`📂 Directory path: ${currentDirectoryPath}`);
    console.log(`📂 OS: ${currentOS}, Path will be formatted appropriately when used`);

    return currentDirectoryPath;
  } catch (error) {
    console.error("Error creating directory structure:", error);
    // Return a safe fallback path
    currentDirectoryPath = "Recordings/Unsorted";
    return currentDirectoryPath;
  }
}

// Function to sanitize filename
function sanitizeFilename(filename) {
  if (!filename) {
    return "unnamed_item";
  }

  // Replace invalid characters with underscores
  const invalidChars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*', ',', ';', '='];
  let sanitized = filename;

  for (const char of invalidChars) {
    // Escape special regex characters to avoid syntax errors
    const escapedChar = char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    sanitized = sanitized.replace(new RegExp(escapedChar, 'g'), '_');
  }

  // Replace multiple underscores with a single one
  while (sanitized.includes('__')) {
    sanitized = sanitized.replace(/__/g, '_');
  }

  // Remove leading/trailing underscores and spaces
  sanitized = sanitized.trim().replace(/(^_+)|(_+$)/g, '');

  // If filename is empty after sanitization, use a default
  if (!sanitized) {
    return "unnamed_item";
  }

  return sanitized;
}

// Function to start recording
function startRecording(options, sectionTitle, itemTitle) {
  try {
    console.log("▶️ Starting new recording");

    // Reset auto-stop flag and video tracking variables when starting a new recording
    window.autoStopTriggered = false;
    window.lastVideoTime = null;
    window.lastTimeCheck = null;

    // Create directory structure if section title is provided
    if (sectionTitle) {
      // Try to get section index from the current item being processed
      const sectionIndex = window.currentSectionIndex !== undefined ?
                          window.currentSectionIndex : null;
      createDirectoryStructure(sectionTitle, sectionIndex);
    }

    // Find video element with more detailed logging
    console.log("🔍 Looking for video element to record...");
    const videoElement = findVideoElement();
    if (!videoElement) {
      console.error("❌ No video element found on the page. URL:", window.location.href);
      return {
        success: false,
        error: 'No video element found on the page. The page might not have loaded properly or the video might be in a format that cannot be detected.'
      };
    }

    // Log video element details for debugging
    console.log("✅ Video element found:", {
      tagName: videoElement.tagName,
      id: videoElement.id,
      className: videoElement.className,
      src: videoElement.src || "No direct src",
      readyState: videoElement.readyState,
      networkState: videoElement.networkState,
      paused: videoElement.paused,
      duration: videoElement.duration
    });

    // Ensure video is loaded
    if (videoElement.readyState < 1) {
      console.log("⏳ Video not fully loaded, waiting for metadata...");
      // Try to load metadata if not already loaded
      videoElement.load();
      // Wait longer for video to load (increased from 2000ms to 5000ms)
      return new Promise(resolve => {
        setTimeout(() => {
          if (videoElement.readyState >= 1) {
            console.log("✅ Video metadata loaded successfully");
            resolve(continueRecording(videoElement, options, sectionTitle, itemTitle));
          } else {
            // Check if the video source is valid
            const currentSrc = videoElement.currentSrc || videoElement.src;
            if (!currentSrc || currentSrc === "" || (currentSrc.startsWith('blob:') && !isValidBlobUrl(currentSrc))) {
              console.error(`❌ Video source is invalid or blob URL is not accessible: ${currentSrc}`);
              resolve({
                success: false,
                error: 'Video source is invalid or not accessible'
              });
            } else {
              console.error("❌ Video failed to load metadata");
              resolve({
                success: false,
                error: 'Video element found but metadata could not be loaded'
              });
            }
          }
        }, 5000); // Increased from 2000ms to 5000ms
      });
    }

    return continueRecording(videoElement, options, sectionTitle, itemTitle);
  } catch (error) {
    console.error('Error starting recording:', error);
    return { success: false, error: error.message };
  }
}

// Helper function to continue recording process after video element is found
function continueRecording(videoElement, options, _sectionTitle, itemTitle) {
  try {
    // Capture stream
    const stream = captureStream(videoElement);
    if (!stream) {
      return { success: false, error: 'Failed to capture video stream' };
    }

    // Setup recorder with selected options
    const recorderInfo = setupRecorder(stream, options.codec, options.quality);
    if (!recorderInfo) {
      return { success: false, error: 'Failed to setup MediaRecorder with selected options' };
    }

    // Start recording
    videoRecorderControl = startRecordingProcess(recorderInfo, videoElement, stream, itemTitle);

    // Set up auto-stop if enabled
    if (options.autoStop) {
      console.log("🔄 Setting up auto-stop detection for video");

      // Function to force stop the video and trigger ended event
      function forceStopVideo(videoEl) {
        console.log("🛑 Force stopping video");

        // Pause the video
        videoEl.pause();

        // Set current time to end of video if duration is available
        if (!isNaN(videoEl.duration) && videoEl.duration > 0) {
          videoEl.currentTime = videoEl.duration;
        }

        // Set flag to indicate auto-stop was triggered
        window.autoStopTriggered = true;
        console.log("🚩 Setting autoStopTriggered flag to true (from forceStopVideo)");

        // Trigger the ended event - this will call the existing ended event handler
        // which will properly stop the recording
        const endEvent = new Event('ended');
        videoEl.dispatchEvent(endEvent);
      }

      // Add event listener for the actual ended event
      let forceStopTimeout = null;
      videoElement.addEventListener('ended', function() {
        console.log("🎬 Video ended (ended event), stopping recording automatically");
        if (videoRecorderControl && !videoRecorderControl.isPaused()) {
          if (videoEndCheckInterval) {
            clearInterval(videoEndCheckInterval);
            videoEndCheckInterval = null;
          }
          if (forceStopTimeout) {
            clearTimeout(forceStopTimeout);
            forceStopTimeout = null;
          }
          // Set a flag to indicate auto-stop was triggered
          window.autoStopTriggered = true;
          console.log("🚩 Setting autoStopTriggered flag to true");
          videoRecorderControl.stop();
          videoRecorderControl = null;
        }
      });

      // Fallback: forcibly stop after video duration + 2 seconds
      console.log('[DEBUG] videoElement.duration:', videoElement.duration, 'readyState:', videoElement.readyState);
      if (!isNaN(videoElement.duration) && videoElement.duration > 0) {
        console.log('[DEBUG] Setting forceStopTimeout for', (videoElement.duration + 2), 'seconds');
        forceStopTimeout = setTimeout(() => {
          console.log('[DEBUG] forceStopTimeout triggered');
          if (videoRecorderControl && !videoRecorderControl.isPaused()) {
            console.log("⏰ Forcibly stopping recording after video duration timeout");
            if (videoEndCheckInterval) {
              clearInterval(videoEndCheckInterval);
              videoEndCheckInterval = null;
            }
            videoRecorderControl.stop();
            videoRecorderControl = null;
          }
        }, (videoElement.duration + 2) * 1000);
      } else {
        console.warn('[DEBUG] Not setting forceStopTimeout: invalid videoElement.duration', videoElement.duration);
      }

      // Check for video completion or buffering issues
      videoEndCheckInterval = setInterval(() => {
        // Skip if video element is no longer valid or paused
        if (!videoElement || videoElement.paused) return;

        // Get current state
        const currentTime = videoElement.currentTime;
        const duration = videoElement.duration;
        const buffering = videoElement.readyState < 3; // Less than HAVE_FUTURE_DATA

        // console.log(`🔍 Video check: time=${currentTime.toFixed(1)}/${duration.toFixed(1)}, buffering=${buffering}, readyState=${videoElement.readyState}`);

        // Check if video is near the end (within 1 second)
        if (!isNaN(duration) && duration > 0 && (duration - currentTime) < 1) {
          console.log("🎬 Video near end, forcing stop");
          forceStopVideo(videoElement);
          return;
        }

        // Check if video is stuck buffering (store last time and check if it hasn't changed)
        if (!window.lastVideoTime) {
          window.lastVideoTime = currentTime;
          window.lastTimeCheck = Date.now();
          return;
        }

        // If time hasn't changed for 5 seconds and video is buffering, force stop
        const timeDiff = Math.abs(currentTime - window.lastVideoTime);
        const timeSinceCheck = (Date.now() - window.lastTimeCheck) / 1000;

        if (timeDiff < 0.1 && timeSinceCheck > 5 && buffering) {
          console.log("⚠️ Video appears to be stuck buffering for 5+ seconds, forcing stop");
          forceStopVideo(videoElement);
          return;
        }

        // Update last checked values
        window.lastVideoTime = currentTime;
        window.lastTimeCheck = Date.now();
      }, 1000); // Check every second
    }

    // Store auto-download preference
    window.autoDownloadAfterRecording = options.autoDownload;

    // Store directory path for download
    window.currentDirectoryPath = currentDirectoryPath;

    return { success: true };
  } catch (error) {
    console.error('Error in continueRecording:', error);
    return { success: false, error: error.message };
  }
}

// Start the actual recording process
function startRecordingProcess(recorderInfo, videoElement, stream, customItemTitle) {
  const { recorder, mimeType } = recorderInfo;
  const chunks = [];
  let startTime = new Date();
  let recordingInterval;
  let isPaused = false;
  let elapsedBeforePause = 0;

  console.log("▶️ Starting recording...");

  // Set up data handling
  recorder.ondataavailable = function(event) {
    if (event.data.size > 0) {
      chunks.push(event.data);
      const sizeMB = (event.data.size / (1024 * 1024)).toFixed(2);
      // console.log(`📦 Data chunk received: ${sizeMB} MB`);

      // Update status in popup and on page
      if (!isPaused) {
        const currentElapsed = ((new Date() - startTime) / 1000) + elapsedBeforePause;
        const status = `Recording: ${currentElapsed.toFixed(0)}s, ${getTotalSize(chunks)}`;

        // Store current status for state queries
        currentRecordingStatus = status;

        // Update popup if it's open
        chrome.runtime.sendMessage({
          action: 'updateStatus',
          status: status
        });

        // Update on-page status indicator
        updatePageStatusIndicator(status, false);
      }
    }
  };

  // Handle recording stop
  recorder.onstop = function() {
    console.log("⏹️ Recording stopped",
                "autoStopTriggered:", window.autoStopTriggered ? "true" : "false",
                "isProcessingBatch:", window.isProcessingBatch ? "true" : "false");
    clearInterval(recordingInterval);

    // Clear video end check interval if it exists
    if (videoEndCheckInterval) {
      console.log("⏱️ Clearing video end check interval");
      clearInterval(videoEndCheckInterval);
      videoEndCheckInterval = null;
    }

    if (chunks.length === 0) {
      console.error("❌ No data was recorded");
      // Remove the status indicator
      removePageStatusIndicator();

      // Notify popup that recording has stopped even if no data was recorded
      chrome.runtime.sendMessage({
        action: 'recordingStopped',
        downloadStarted: false,
        error: 'No data was recorded'
      });

      return;
    }

    const totalSize = getTotalSize(chunks);
    console.log(`✅ Recording completed: ${chunks.length} chunks, ${totalSize}`);

    // Create blob with proper metadata for seeking
    console.log("🔄 Processing video chunks and adding metadata for seeking...");

    // Create blob with proper type
    const blob = new Blob(chunks, {
      type: mimeType
    });

    // Create object URL from the blob
    const url = URL.createObjectURL(blob);

    // Use custom item title if provided, otherwise get from page
    let videoTitle = customItemTitle;
    if (!videoTitle) {
      videoTitle = getVideoTitle();
    }

    // Sanitize the filename
    videoTitle = sanitizeFilename(videoTitle);

    // Determine file extension based on mime type
    const fileExtension = mimeType.includes('mp4') ? 'mp4' : 'webm';

    // Create filename with directory path if available
    let filename = `${videoTitle}.${fileExtension}`;

    // If we have a directory path, prepend it
    if (window.currentDirectoryPath) {
      console.log(`📂 Using directory path: ${window.currentDirectoryPath}`);
      // Store the directory path for potential use by browser extensions
      window.lastVideoDownloadPath = window.currentDirectoryPath;
      window.lastVideoFilename = filename;
    }

    // Update status indicator to show completion
    updatePageStatusIndicator(`Recording complete: ${totalSize}`, true);

    // Create download link
    createDownloadLink(url, filename);

    console.log(`💾 Video saved as: ${filename}`);

    // Show countdown on page before moving to next video
    if (window.isProcessingBatch) {
      console.log("🔄 Batch processing: Starting countdown for next video");
      // Create or update countdown UI
      const countdownUI = createCountdownUI();

      // Start countdown
      let countdown = 5;
      updateCountdownUI(countdownUI, `Moving to next video in ${countdown}...`);

      const countdownInterval = setInterval(() => {
        countdown--;
        console.log(`⏱️ Batch countdown: ${countdown} seconds remaining`);

        if (countdown <= 0) {
          console.log("✅ Batch countdown complete, proceeding to next video");
          clearInterval(countdownInterval);
          // Remove countdown UI
          if (countdownUI && countdownUI.parentNode) {
            countdownUI.parentNode.removeChild(countdownUI);
          }

          // Notify popup that recording has stopped and download has started
          console.log("📤 Sending recordingStopped message to popup for batch processing",
                     "autoStopTriggered:", window.autoStopTriggered ? "true" : "false");
          chrome.runtime.sendMessage({
            action: 'recordingStopped',
            downloadStarted: window.autoDownloadAfterRecording,
            filename: filename,
            directoryPath: window.currentDirectoryPath,
            isBatch: true,  // Add explicit flag to indicate this is part of batch processing
            autoStopTriggered: window.autoStopTriggered || false,  // Pass the auto-stop flag
            forceNextVideo: window.autoStopTriggered || false  // Also set forceNextVideo if auto-stopped
          }, response => {
            console.log("📥 Received response from recordingStopped message:", response);
            // If no response or error, try again
            if (!response) {
              console.warn("⚠️ No response received from popup, retrying message");
              setTimeout(() => {
                chrome.runtime.sendMessage({
                  action: 'recordingStopped',
                  downloadStarted: window.autoDownloadAfterRecording,
                  filename: filename,
                  directoryPath: window.currentDirectoryPath,
                  isBatch: true,
                  autoStopTriggered: window.autoStopTriggered || false,
                  forceNextVideo: window.autoStopTriggered || false,
                  retry: true
                });
              }, 500);
            }
          });
        } else {
          updateCountdownUI(countdownUI, `Moving to next video in ${countdown}...`);
        }
      }, 1000);
    } else {
      // If not processing batch, just notify popup immediately
      setTimeout(() => {
        console.log("📤 Sending recordingStopped message to popup (non-batch)",
                   "autoStopTriggered:", window.autoStopTriggered ? "true" : "false");
        chrome.runtime.sendMessage({
          action: 'recordingStopped',
          downloadStarted: window.autoDownloadAfterRecording,
          filename: filename,
          directoryPath: window.currentDirectoryPath,
          autoStopTriggered: window.autoStopTriggered || false,  // Pass the auto-stop flag
          forceNextVideo: window.autoStopTriggered || false  // Also set forceNextVideo if auto-stopped
        });
      }, 1000); // Small delay to ensure download has started
    }
  };

  // Handle errors
  recorder.onerror = function(event) {
    console.error("❌ MediaRecorder error:", event);
  };

  // Reset video to beginning if needed
  if (videoElement.currentTime > 0) {
    console.log("⏮️ Resetting video to beginning");
    videoElement.currentTime = 0;
  }

  // Make sure video is playing
  if (videoElement.paused) {
    console.log("▶️ Starting video playback");
    videoElement.play()
      .then(() => console.log("✅ Video playback started"))
      .catch(err => console.error("❌ Error playing video:", err));
  }

  // Start recording with smaller chunks for better seeking
  console.log("🎬 Starting MediaRecorder with optimized settings for seeking...");
  recorder.start(100); // Collect data every 100ms for more precise seeking
  console.log("✅ Recording started at", new Date().toLocaleTimeString());

  // Return control functions
  return {
    stop: () => {
      console.log("🛑 Stopping recording...");
      recorder.stop();
      stream.getTracks().forEach(track => track.stop());

      // Clear video end check interval if it exists
      if (videoEndCheckInterval) {
        clearInterval(videoEndCheckInterval);
        videoEndCheckInterval = null;
      }

      // Reset video tracking variables
      window.lastVideoTime = null;
      window.lastTimeCheck = null;
    },
    pause: () => {
      if (!isPaused && recorder.state === 'recording') {
        console.log("⏸️ Pausing recording...");
        recorder.pause();
        videoElement.pause();
        isPaused = true;
        elapsedBeforePause += (new Date() - startTime) / 1000;

        const status = `Paused: ${elapsedBeforePause.toFixed(0)}s, ${getTotalSize(chunks)}`;

        // Store current status for state queries
        currentRecordingStatus = status;

        // Update popup if it's open
        chrome.runtime.sendMessage({
          action: 'updateStatus',
          status: status
        });

        // Update on-page status indicator
        updatePageStatusIndicator(status, true);

        return true;
      }
      return false;
    },
    resume: () => {
      if (isPaused && recorder.state === 'paused') {
        console.log("▶️ Resuming recording...");
        recorder.resume();
        videoElement.play()
          .then(() => console.log("✅ Video playback resumed"))
          .catch(err => console.error("❌ Error resuming video:", err));
        isPaused = false;
        startTime = new Date(); // Reset start time for new elapsed time calculation

        // Update on-page status indicator
        const status = `Recording resumed...`;

        // Store current status for state queries
        currentRecordingStatus = status;

        // Update on-page status indicator
        updatePageStatusIndicator(status, false);

        // Update popup if it's open
        chrome.runtime.sendMessage({
          action: 'updateStatus',
          status: status
        });

        return true;
      }
      return false;
    },
    isPaused: () => isPaused,
    getChunks: () => chunks
  };
}

// Helper function to get total size of chunks
function getTotalSize(chunks) {
  const totalBytes = chunks.reduce((total, chunk) => total + chunk.size, 0);
  if (totalBytes < 1024 * 1024) {
    return (totalBytes / 1024).toFixed(2) + " KB";
  } else {
    return (totalBytes / (1024 * 1024)).toFixed(2) + " MB";
  }
}

// Helper function to format date for filename
function formatDate(date) {
  return date.toISOString()
    .replace(/[T:]/g, '-')
    .replace(/\..+/, '')
    .replace(/[^0-9-]/g, '');
}

// Helper function to check if a blob URL is valid
function isValidBlobUrl(url) {
  if (!url || !url.startsWith('blob:')) return false;

  try {
    // Try to create a request to check if the blob is accessible
    const xhr = new XMLHttpRequest();
    xhr.open('HEAD', url, false); // Synchronous request
    xhr.send();
    return xhr.status === 200;
  } catch (e) {
    console.error(`❌ Error checking blob URL: ${e.message}`);
    return false;
  }
}

// Helper function to get video title
function getVideoTitle() {
  // Try to get the currently selected/active section item (matches Python logic)
  const selectedItem = document.querySelector('div.courseSubItem.selected');
  if (selectedItem) {
    const titleElement = selectedItem.querySelector('div.title');
    if (titleElement) {
      return titleElement.textContent.trim();
    }
  }
  // Fallback to old logic
  const titleElement = document.querySelector('.video-title') ||
                       document.querySelector('h1') ||
                       document.querySelector('title');
  if (titleElement) {
    return titleElement.textContent.trim();
  }
  return 'video_recording';
}

// Create download link
function createDownloadLink(url, filename) {
  // If we have a directory path, include it in the suggested filename
  let downloadFilename = filename;
  if (window.currentDirectoryPath) {
    // Create proper directory structure using platform-appropriate path separator
    // First normalize the path to use forward slashes (standard for paths in JavaScript)
    let normalizedPath = window.currentDirectoryPath.replace(/\\/g, '/');

    // For Chrome downloads API:
    // - Windows: Use backslashes
    // - macOS/Linux: Use forward slashes
    if (currentOS === 'windows') {
      // Convert to Windows format for Windows OS
      const formattedPath = normalizedPath.replace(/\//g, '\\');
      downloadFilename = `${formattedPath}\\${filename}`;
    } else {
      // Keep forward slashes for macOS/Linux
      downloadFilename = `${normalizedPath}/${filename}`;
    }

    console.log(`📂 Using directory structure for download: ${downloadFilename}`);
    console.log(`📂 OS: ${currentOS}, Path separator: ${PATH_SEPARATOR}`);
  }

  // Auto-download if enabled
  if (window.autoDownloadAfterRecording) {
    // Use Chrome's downloads API to create proper directory structure
    chrome.runtime.sendMessage({
      action: 'downloadFile',
      url: url,
      filename: downloadFilename,
      os: currentOS  // Send OS info to background script
    }, function(response) {
      if (response?.success) {
        console.log(`✅ Download started with ID: ${response.downloadId}`);
      } else {
        console.error(`❌ Download failed: ${response?.error || 'Unknown error'}`);
        // Fallback to regular download if Chrome API fails
        fallbackDownload(url, filename);
      }
    });
  } else {
    // Create visible download UI
    createDownloadUI(url, filename, downloadFilename);
  }
}

// Fallback download method using regular anchor element
function fallbackDownload(url, filename) {
  // Create a hidden download link
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  document.body.appendChild(link);

  setTimeout(() => {
    link.click();
    console.log('✅ Download started using fallback method');
    // Clean up
    setTimeout(() => {
      document.body.removeChild(link);
    }, 1000);
  }, 500);
}

// Create and update the on-page status indicator
function updatePageStatusIndicator(status, isPaused) {
  // Create the status indicator if it doesn't exist
  if (!recordingStatusUI) {
    recordingStatusUI = document.createElement('div');
    recordingStatusUI.id = 'video-recorder-status-ui';
    recordingStatusUI.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px 15px;
      border-radius: 8px;
      z-index: 9999;
      font-family: Arial, sans-serif;
      font-size: 14px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
      display: flex;
      align-items: center;
    `;

    // Add a recording icon
    const recordingIcon = document.createElement('div');
    recordingIcon.id = 'recording-icon';
    recordingIcon.style.cssText = `
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #f44336;
      margin-right: 10px;
      animation: pulse 1.5s infinite;
    `;

    // Add animation style
    const style = document.createElement('style');
    style.textContent = `
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }
    `;
    document.head.appendChild(style);

    recordingStatusUI.appendChild(recordingIcon);

    // Add status text
    const statusText = document.createElement('div');
    statusText.id = 'recording-status-text';
    recordingStatusUI.appendChild(statusText);

    // Add to page
    document.body.appendChild(recordingStatusUI);
  }

  // Update the status text
  const statusText = document.getElementById('recording-status-text');
  if (statusText) {
    statusText.textContent = status;
  }

  // Update the recording icon based on paused state
  const recordingIcon = document.getElementById('recording-icon');
  if (recordingIcon) {
    if (isPaused) {
      recordingIcon.style.backgroundColor = '#FF9800';
      recordingIcon.style.animation = 'none';
    } else {
      recordingIcon.style.backgroundColor = '#f44336';
      recordingIcon.style.animation = 'pulse 1.5s infinite';
    }
  }
}

// Remove the on-page status indicator
function removePageStatusIndicator() {
  if (recordingStatusUI) {
    recordingStatusUI.remove();
    recordingStatusUI = null;
  }
}

// Create countdown UI for batch processing
function createCountdownUI() {
  // Remove existing countdown UI if it exists
  const existingUI = document.getElementById('video-recorder-countdown-ui');
  if (existingUI) {
    existingUI.remove();
  }

  // Create new countdown UI
  const countdownUI = document.createElement('div');
  countdownUI.id = 'video-recorder-countdown-ui';
  countdownUI.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px 30px;
    border-radius: 8px;
    z-index: 10000;
    font-family: Arial, sans-serif;
    font-size: 18px;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0,0,0,0.5);
    text-align: center;
  `;

  // Add to page
  document.body.appendChild(countdownUI);

  return countdownUI;
}

// Update countdown UI with message
function updateCountdownUI(countdownUI, message) {
  if (countdownUI) {
    countdownUI.textContent = message;
  }
}

// Create a visible download UI
function createDownloadUI(url, filename, downloadFilename) {
  // Remove existing UI if present
  const existingUI = document.getElementById('video-recorder-download-ui');
  if (existingUI) {
    existingUI.remove();
  }

  // Remove the status indicator
  removePageStatusIndicator();

  // Create UI container
  const ui = document.createElement('div');
  ui.id = 'video-recorder-download-ui';
  ui.style.cssText = `
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 15px;
    border-radius: 8px;
    z-index: 9999;
    font-family: Arial, sans-serif;
    width: 300px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
  `;

  // Create title
  const title = document.createElement('div');
  title.textContent = '🎬 Recording Complete';
  title.style.cssText = `
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
  `;

  // Create path info if available
  if (window.currentDirectoryPath) {
    const pathInfo = document.createElement('div');
    pathInfo.textContent = `📂 Path: ${window.currentDirectoryPath}`;
    pathInfo.style.cssText = `
      font-size: 12px;
      margin-bottom: 10px;
      color: #ccc;
      word-break: break-all;
    `;
    ui.appendChild(pathInfo);
  }

  // Create download button
  const downloadButton = document.createElement('a');
  downloadButton.href = url;
  downloadButton.download = downloadFilename || filename;
  downloadButton.textContent = 'Download Recording';
  downloadButton.style.cssText = `
    display: block;
    background: #4CAF50;
    color: white;
    text-decoration: none;
    padding: 10px;
    border-radius: 4px;
    text-align: center;
    margin-top: 10px;
    font-weight: bold;
  `;

  // Add click handler to use Chrome downloads API if available
  downloadButton.addEventListener('click', function(e) {
    if (downloadFilename && downloadFilename !== filename) {
      e.preventDefault();
      chrome.runtime.sendMessage({
        action: 'downloadFile',
        url: url,
        filename: downloadFilename,
        os: currentOS  // Send OS info to background script
      }, function(response) {
        if (!response?.success) {
          console.error(`❌ Download failed: ${response?.error || 'Unknown error'}`);
          // Fallback to regular download
          fallbackDownload(url, filename);
        }
      });
    }
  });

  // Add a preview button to test video with seeking
  const previewButton = document.createElement('button');
  previewButton.textContent = 'Preview Video';
  previewButton.style.cssText = `
    display: block;
    background: #2196F3;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
    margin-top: 10px;
    font-weight: bold;
  `;

  // Add preview functionality
  previewButton.addEventListener('click', function() {
    // Create a video preview element
    const videoPreview = document.createElement('video');
    videoPreview.controls = true;
    videoPreview.autoplay = false;
    videoPreview.src = url;
    videoPreview.style.cssText = `
      width: 100%;
      max-height: 400px;
      margin-top: 10px;
      border-radius: 4px;
    `;

    // Add seeking optimization attributes
    videoPreview.preload = "auto";
    videoPreview.controlsList = "nodownload";

    // Check if preview already exists
    const existingPreview = ui.querySelector('video');
    if (existingPreview) {
      ui.removeChild(existingPreview);
    }

    // Insert before the close button
    ui.insertBefore(videoPreview, closeButton);

    console.log("🎬 Video preview created with seeking controls");
  });

  // Add preview button to UI
  ui.appendChild(previewButton);

  // Create close button
  const closeButton = document.createElement('button');
  closeButton.textContent = 'Close';
  closeButton.style.cssText = `
    display: block;
    background: #f44336;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
    margin-top: 10px;
    font-weight: bold;
  `;
  closeButton.onclick = () => ui.remove();

  // Add elements to UI
  ui.appendChild(title);
  ui.appendChild(downloadButton);
  // Preview button is added before this point
  ui.appendChild(closeButton);

  // Add UI to page
  document.body.appendChild(ui);
}

// Find the video element on the page
function findVideoElement() {
  console.log("🔍 Searching for video element...");

  // Try multiple selectors to find the video element
  // First try the standard video tag
  let videoElement = document.querySelector('video');

  // If not found, try to find video with specific attributes
  if (!videoElement) {
    videoElement = document.querySelector('video[class*="video"]');
  }

  // If still not found, try to find video inside specific containers
  if (!videoElement) {
    const videoContainer = document.querySelector('.video-container') ||
                          document.querySelector('[class*="video-player"]') ||
                          document.querySelector('[class*="player"]');

    if (videoContainer) {
      videoElement = videoContainer.querySelector('video');
    }
  }

  // If still not found, try to find iframe that might contain video
  if (!videoElement) {
    const iframe = document.querySelector('iframe');
    if (iframe) {
      try {
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        videoElement = iframeDoc.querySelector('video');
      } catch (e) {
        console.warn("Cannot access iframe content due to same-origin policy", e);
      }
    }
  }

  if (!videoElement) {
    console.error("❌ No video element found on the page");
    return null;
  }

  console.log("✅ Video element found:", videoElement);
  return videoElement;
}

// Capture the video stream
function captureStream(videoElement) {
  console.log("🔄 Attempting to capture video stream...");

  // Check if video has valid source
  if (videoElement.error) {
    console.error("❌ Video element has an error:", videoElement.error.code, videoElement.error.message);
    return null;
  }

  // Check if video is in a valid state
  if (videoElement.networkState === HTMLMediaElement.NETWORK_NO_SOURCE ||
      videoElement.networkState === HTMLMediaElement.NETWORK_EMPTY) {
    console.error("❌ Video has no source or empty network state:", videoElement.networkState);
    return null;
  }

  if (typeof videoElement.captureStream !== 'function') {
    console.error("❌ captureStream() is not supported in this browser");
    return null;
  }

  try {
    // Try to ensure video is ready
    if (videoElement.readyState === 0) {
      console.log("⚠️ Video not ready, attempting to load...");
      videoElement.load();
    }

    const stream = videoElement.captureStream();

    if (!stream) {
      console.error("❌ Failed to capture stream - stream is null");
      return null;
    }

    // Verify stream has video tracks
    if (stream.getVideoTracks().length === 0) {
      console.error("❌ Stream has no video tracks");
      return null;
    }

    console.log("✅ Stream captured successfully with", stream.getVideoTracks().length, "video tracks and",
                stream.getAudioTracks().length, "audio tracks");
    return stream;
  } catch (error) {
    console.error("❌ Error capturing stream:", error.message);
    return null;
  }
}

// Set up the MediaRecorder with selected options
function setupRecorder(stream, codecOption, qualityOption) {
  console.log("🎥 Setting up MediaRecorder...");

  if (typeof MediaRecorder === 'undefined') {
    console.error("❌ MediaRecorder API is not supported in this browser");
    return null;
  }

  // Quality settings
  const qualitySettings = {
    low: { videoBitsPerSecond: 1000000 },    // 1 Mbps
    medium: { videoBitsPerSecond: 2500000 }, // 2.5 Mbps
    high: { videoBitsPerSecond: 5000000 },   // 5 Mbps
    ultra: { videoBitsPerSecond: 8000000 }   // 8 Mbps
  };

  // Codec options with descriptions
  const codecOptions = {
    vp9: {
      name: 'VP9 + Opus (WebM)',
      mimeTypes: ['video/webm;codecs=vp9,opus']
    },
    vp8: {
      name: 'VP8 + Opus (WebM)',
      mimeTypes: ['video/webm;codecs=vp8,opus']
    },
    av1: {
      name: 'AV1 + Opus (WebM)',
      mimeTypes: ['video/webm;codecs=av1,opus']
    },
    h265: {
      name: 'H.265 + AAC (MP4)',
      mimeTypes: ['video/mp4;codecs=hev1,mp4a.40.2', 'video/mp4;codecs=hvc1,mp4a.40.2']
    },
    h264: {
      name: 'H.264 + AAC (MP4)',
      mimeTypes: ['video/mp4;codecs=avc1.42E01E,mp4a.40.2']
    },
    webm: {
      name: 'WebM (Default)',
      mimeTypes: ['video/webm']
    },
    mp4: {
      name: 'MP4 (Default)',
      mimeTypes: ['video/mp4']
    }
  };

  // Get the selected codec's mime types or default to all mime types
  const mimeTypesToTry = codecOption && codecOptions[codecOption]
    ? codecOptions[codecOption].mimeTypes
    : Object.values(codecOptions).flatMap(codec => codec.mimeTypes);

  // Add fallback options at the end
  const allMimeTypes = [...mimeTypesToTry, 'video/webm', 'video/mp4'];

  // Find the first supported MIME type
  let selectedMimeType = null;
  for (const type of allMimeTypes) {
    if (MediaRecorder.isTypeSupported(type)) {
      selectedMimeType = type;
      console.log(`✅ Using MIME type: ${type}`);
      break;
    }
  }

  if (!selectedMimeType) {
    console.error("❌ No supported MIME types found for MediaRecorder");
    return null;
  }

  // Get quality settings based on selected quality
  const settings = qualitySettings[qualityOption] || qualitySettings.high;

  try {
    // Create recorder with enhanced options for better seeking
    const recorder = new MediaRecorder(stream, {
      mimeType: selectedMimeType,
      videoBitsPerSecond: settings.videoBitsPerSecond,
      // Add these options to improve seeking capability
      audioBitsPerSecond: 128000, // 128 kbps for audio
      // Set to true to generate a self-contained file with proper metadata
      ignoreMutedMedia: false
    });

    console.log("✅ MediaRecorder created successfully");
    return {
      recorder,
      mimeType: selectedMimeType
    };
  } catch (error) {
    console.error("❌ Error creating MediaRecorder:", error.message);
    return null;
  }
}
