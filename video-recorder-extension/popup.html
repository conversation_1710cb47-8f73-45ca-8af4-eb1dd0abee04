<!DOCTYPE html>
<html>
<head>
  <title>Video Recorder</title>
  <meta charset="utf-8">
  <style>
    body {
      font-family: Arial, sans-serif;
      width: 380px;
      padding: 15px;
      background-color: #f5f5f5;
      margin: 0;
    }

    h1 {
      font-size: 18px;
      text-align: center;
      margin-bottom: 15px;
      color: #333;
    }

    .form-group {
      margin-bottom: 12px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #555;
    }

    select, button {
      width: 100%;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ccc;
    }

    select {
      background-color: white;
    }

    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 10px;
      cursor: pointer;
      font-weight: bold;
      margin-top: 10px;
    }

    button:hover {
      background-color: #45a049;
    }

    .checkbox-group {
      margin-top: 10px;
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      font-weight: normal;
    }

    .checkbox-label input {
      margin-right: 8px;
    }

    #status {
      margin-top: 15px;
      padding: 10px;
      border-radius: 4px;
      text-align: center;
      display: none;
    }

    .success {
      background-color: #dff0d8;
      color: #3c763d;
    }

    .error {
      background-color: #f2dede;
      color: #a94442;
    }

    .recording-controls {
      display: none;
      margin-top: 15px;
    }

    .control-buttons {
      display: flex;
      justify-content: space-between;
    }

    .control-buttons button {
      flex: 1;
      margin: 0 5px;
    }

    .pause-button {
      background-color: #FF9800;
    }

    .stop-button {
      background-color: #f44336;
    }

    .recording-status {
      margin-top: 10px;
      text-align: center;
      font-weight: bold;
    }

    /* Tab styles */
    .tab-container {
      margin-bottom: 15px;
    }

    .tab-buttons {
      display: flex;
      width: 100%;
      margin-bottom: 15px;
    }

    .tab-button {
      flex: 1;
      padding: 10px 15px;
      background-color: #f1f1f1;
      border: none;
      cursor: pointer;
      font-weight: bold;
      text-align: center;
      border-bottom: 1px solid #ccc;
      color: #222;
      transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
      border-radius: 8px 8px 0 0;
      margin-right: 2px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.04);
    }

    .tab-button.active {
      background-color: #4CAF50;
      color: white;
      border-bottom: 1px solid #4CAF50;
      box-shadow: 0 4px 12px rgba(76,175,80,0.15);
      z-index: 2;
    }

    .tab-content {
      display: none;
      background-color: #fff;
      border-radius: 4px;
      padding: 15px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .tab-content.active {
      display: block;
      animation: fadeInTab 0.4s;
    }

    @keyframes fadeInTab {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* Course structure tree styles */
    .course-tree {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      background-color: white;
      margin-bottom: 15px;
    }

    .course-title {
      font-weight: bold;
      font-size: 16px;
      margin-bottom: 10px;
      padding-bottom: 5px;
      border-bottom: 1px solid #eee;
      color: #333;
    }

    .tree-item {
      margin: 8px 0;
    }

    .section {
      font-weight: bold;
      cursor: pointer;
      padding: 8px;
      background-color: #f5f5f5;
      border-radius: 4px;
      display: flex;
      align-items: center;
      border: 1px solid #e0e0e0;
    }

    .section-toggle {
      margin-right: 8px;
      font-size: 12px;
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
      color: #555;
    }

    .section-items {
      margin-left: 20px;
      display: none;
      padding-left: 10px;
      border-left: 1px dashed #ccc;
      margin-top: 5px;
    }

    .section-items.expanded {
      display: block;
    }

    .item {
      padding: 6px 8px;
      display: flex;
      align-items: center;
      margin: 4px 0;
      border-radius: 3px;
      background-color: #f9f9f9;
    }

    .item:hover {
      background-color: #f0f0f0;
    }

    .item-checkbox, .section-checkbox {
      margin-right: 8px;
      width: 16px;
      height: 16px;
    }

    .item-title, .section-title {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
    }

    .item-type {
      margin-left: 5px;
      font-size: 11px;
      color: #fff;
      background-color: #888;
      padding: 2px 6px;
      border-radius: 10px;
      text-transform: uppercase;
      font-weight: bold;
    }

    .item-type.video {
      background-color: #4285F4;
    }

    .item-type.pdf {
      background-color: #DB4437;
    }

    .item-type.quiz {
      background-color: #F4B400;
    }

    .item-type.assignment {
      background-color: #0F9D58;
    }

    .batch-controls {
      margin-top: 15px;
    }

    .loading-message {
      text-align: center;
      padding: 20px;
      color: #666;
    }

    .error-message {
      text-align: center;
      padding: 20px;
      color: #a94442;
      background-color: #f2dede;
      border-radius: 4px;
    }

    .empty-message {
      padding: 10px;
      color: #666;
      font-style: italic;
    }

    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(0,0,0,0.1);
      border-radius: 50%;
      border-top-color: #4CAF50;
      animation: spin 1s ease-in-out infinite;
      margin-right: 10px;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .progress-bar-container {
      width: 100%;
      height: 20px;
      background-color: #f1f1f1;
      border-radius: 4px;
      margin-top: 10px;
      overflow: hidden;
    }

    .progress-bar {
      height: 100%;
      background-color: #4CAF50;
      width: 0%;
      transition: width 0.3s;
    }

    .progress-text {
      text-align: center;
      margin-top: 5px;
      font-size: 12px;
    }

    .hidden {
      display: none;
    }

    .select-button {
      background-color: #2196F3;
    }

    .deselect-button {
      background-color: #9E9E9E;
    }

    .batch-progress-header {
      display: flex;
      align-items: center;
    }

    .button-row {
      display: flex;
      justify-content: space-between;
      gap: 10px;
      margin-bottom: 15px;
    }

    .button-row button {
      flex: 1;
      margin-top: 0;
    }

    .refresh-button {
      background-color: #2196F3;
      color: white;
    }
  </style>
</head>
<body>
  <h1>🎬 Video Recorder</h1>

  <div class="tab-container">
    <div class="tab-buttons">
      <button type="button" class="tab-button active" data-tab="single-video">Single Video</button>
      <button type="button" class="tab-button" data-tab="course-structure">Course Structure</button>
    </div>

    <div class="tab-content active" id="single-video-tab">
      <div id="current-video-title" style="display:none; text-align:center; font-weight:bold; font-size:16px; margin-bottom:12px;"></div>
      <div id="setup-form">
        <div class="form-group">
          <label for="codec-select">Codec:</label>
          <select id="codec-select">
            <option value="vp9">VP9 + Opus (WebM) - High quality</option>
            <option value="vp8">VP8 + Opus (WebM) - Good quality</option>
            <option value="av1">AV1 + Opus (WebM) - Very high quality</option>
            <option value="h265">H.265 + AAC (MP4) - Very high quality</option>
            <option value="h264">H.264 + AAC (MP4) - Moderate quality</option>
            <option value="webm">WebM (Default container)</option>
            <option value="mp4">MP4 (Default container)</option>
          </select>
        </div>

        <div class="form-group">
          <label for="quality-select">Quality:</label>
          <select id="quality-select">
            <option value="low">Low (1 Mbps)</option>
            <option value="medium">Medium (2.5 Mbps)</option>
            <option value="high" selected>High (5 Mbps)</option>
            <option value="ultra">Ultra (8 Mbps)</option>
          </select>
        </div>

        <div class="checkbox-group">
          <label class="checkbox-label">
            <input type="checkbox" id="auto-stop-checkbox" checked>
            Auto-stop at video end
          </label>
        </div>

        <div class="checkbox-group">
          <label class="checkbox-label">
            <input type="checkbox" id="auto-download-checkbox" checked>
            Auto-download after recording
          </label>
        </div>

        <button id="start-recording" type="button">Start Recording</button>
      </div>
    </div>

    <div class="tab-content" id="course-structure-tab">
      <!-- Batch recording status card - initially hidden -->
      <div id="batch-recording-status-card" style="display: none; margin-bottom: 15px; padding: 12px; background-color: #f9f9f9; border-radius: 8px; border: 1px solid #e0e0e0;">
        <div id="batch-current-video-title" style="text-align:center; font-weight:bold; font-size:16px; margin-bottom:10px;"></div>
        <div class="recording-status" id="batch-recording-status">Recording...</div>
        <div class="control-buttons">
          <button id="batch-pause-resume" type="button" class="pause-button">Pause</button>
          <button id="batch-stop-recording" type="button" class="stop-button">Stop Recording</button>
        </div>
      </div>

      <div class="button-row">
        <button id="parse-course-structure" type="button">Parse Course Structure</button>
        <button id="refresh-course-structure" type="button" class="refresh-button">Refresh</button>
      </div>

      <div id="course-tree-container" style="display: none;">
        <div class="course-tree" id="course-tree">
          <div class="loading-message">Click "Parse Course Structure" to load the course content.</div>
        </div>

        <div class="batch-controls">
          <div class="checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" id="batch-auto-stop-checkbox" checked>
              Auto-stop at video end
            </label>
          </div>

          <div class="checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" id="batch-auto-download-checkbox" checked>
              Auto-download after recording
            </label>
          </div>

          <button id="record-selected" type="button">Record Selected Videos</button>
          <button id="select-all-videos" type="button" class="select-button">Select All Videos</button>
          <button id="deselect-all" type="button" class="deselect-button">Deselect All</button>
        </div>

        <div id="batch-progress" style="display: none; margin-top: 15px;">
          <div style="display: flex; align-items: center;">
            <div class="loading-spinner"></div>
            <div id="batch-status">Processing videos...</div>
          </div>
          <div class="progress-bar-container">
            <div class="progress-bar" id="batch-progress-bar"></div>
          </div>
          <div class="progress-text" id="batch-progress-text">0/0 videos processed</div>
        </div>
      </div>
    </div>
  </div>

  <div id="recording-controls" class="recording-controls">
    <div class="recording-status" id="recording-status">Recording...</div>
    <div class="control-buttons">
      <button id="pause-resume" type="button" class="pause-button">Pause</button>
      <button id="stop-recording" type="button" class="stop-button">Stop Recording</button>
    </div>
  </div>

  <div id="status"></div>

  <script src="popup.js"></script>
</body>
</html>
